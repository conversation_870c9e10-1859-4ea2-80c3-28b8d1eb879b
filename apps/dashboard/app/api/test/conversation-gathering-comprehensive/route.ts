import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';
import { conversationProcessor } from '~/lib/conversation-processor';
import { followerProcessingQueue } from '~/lib/follower-processing-queue';

export async function POST(req: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: { userId: session.user.id }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const organizationId = membership.organizationId;
    const testResults = await runComprehensiveTests(organizationId);

    return NextResponse.json({
      success: true,
      organizationId,
      testResults,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in comprehensive conversation gathering tests:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function runComprehensiveTests(organizationId: string) {
  const results = {
    tests: [] as any[],
    summary: {
      total: 0,
      passed: 0,
      failed: 0,
      warnings: 0
    }
  };

  // Test 1: Basic Configuration Check
  await runTest(results, 'Configuration Check', async () => {
    const instagramSettings = await prisma.instagramSettings.findFirst({
      where: { organizationId, instagramToken: { not: null } }
    });

    const extensionSettings = await prisma.chromeExtensionSettings.findUnique({
      where: { organizationId }
    });

    return {
      hasInstagramToken: !!instagramSettings?.instagramToken,
      hasExtensionSettings: !!extensionSettings,
      extensionStatus: extensionSettings?.extensionStatus
    };
  });

  // Test 2: Conversation Data Integrity
  await runTest(results, 'Conversation Data Integrity', async () => {
    const conversations = await prisma.instagramConversationsNotGathered.findMany({
      where: { organizationId }
    });

    const duplicates = await prisma.instagramConversationsNotGathered.groupBy({
      by: ['instagramConversationId'],
      where: { organizationId },
      having: { instagramConversationId: { _count: { gt: 1 } } }
    });

    const orphanedConversations = [];
    for (const conv of conversations) {
      const hasContact = await prisma.instagramContact.findFirst({
        where: { organizationId, instagramNickname: conv.participantUsername }
      });
      const hasFollower = await prisma.instagramFollower.findFirst({
        where: { organizationId, instagramNickname: conv.participantUsername }
      });

      if (conv.isGathered && !hasContact && !hasFollower) {
        orphanedConversations.push(conv.participantUsername);
      }
    }

    return {
      totalConversations: conversations.length,
      gatheredConversations: conversations.filter(c => c.isGathered).length,
      duplicates: duplicates.length,
      orphanedConversations: orphanedConversations.length,
      orphanedUsernames: orphanedConversations.slice(0, 5) // Show first 5
    };
  });

  // Test 3: Queue Processing Health
  await runTest(results, 'Queue Processing Health', async () => {
    const queueItems = await prisma.followerProcessingQueue.findMany({
      where: { organizationId },
      include: { InstagramFollower: true }
    });

    const stuckItems = queueItems.filter(item => 
      item.status === 'processing' && 
      Date.now() - new Date(item.updatedAt).getTime() > 300000 // 5 minutes
    );

    const failedItems = queueItems.filter(item => item.status === 'failed');
    const pendingWithConversations = queueItems.filter(item => 
      item.status === 'pending' && item.hasConversation
    );

    return {
      totalQueueItems: queueItems.length,
      pendingItems: queueItems.filter(i => i.status === 'pending').length,
      processingItems: queueItems.filter(i => i.status === 'processing').length,
      completedItems: queueItems.filter(i => i.status === 'completed').length,
      failedItems: failedItems.length,
      stuckItems: stuckItems.length,
      pendingWithConversations: pendingWithConversations.length,
      stuckUsernames: stuckItems.map(i => i.InstagramFollower?.instagramNickname).slice(0, 5)
    };
  });

  // Test 4: Conversation Processing Stats
  await runTest(results, 'Conversation Processing Stats', async () => {
    const stats = await conversationProcessor.getProcessingStats(organizationId);
    
    const isStuck = stats.totalConversations > 0 && 
                   stats.gatheredConversations < stats.totalConversations &&
                   stats.pendingQueueItems > 0;

    return {
      ...stats,
      isStuck,
      efficiency: stats.totalConversations > 0 ? 
        (stats.gatheredConversations / stats.totalConversations) * 100 : 0
    };
  });

  // Test 5: Data Consistency Check
  await runTest(results, 'Data Consistency Check', async () => {
    // Check for contacts without corresponding conversations
    const contacts = await prisma.instagramContact.findMany({
      where: { organizationId, conversationSource: 'api' }
    });

    const contactsWithoutConversations = [];
    for (const contact of contacts) {
      const hasConversation = await prisma.instagramConversationsNotGathered.findFirst({
        where: { organizationId, participantUsername: contact.instagramNickname }
      });
      if (!hasConversation) {
        contactsWithoutConversations.push(contact.instagramNickname);
      }
    }

    // Check for mismatched queue items
    const queueMismatches = await prisma.followerProcessingQueue.findMany({
      where: {
        organizationId,
        hasConversation: true,
        status: 'pending'
      },
      include: { InstagramFollower: true }
    });

    const actuallyHaveConversations = [];
    for (const item of queueMismatches) {
      if (item.InstagramFollower) {
        const hasConv = await prisma.instagramConversationsNotGathered.findFirst({
          where: { 
            organizationId, 
            participantUsername: item.InstagramFollower.instagramNickname 
          }
        });
        if (hasConv) {
          actuallyHaveConversations.push(item.InstagramFollower.instagramNickname);
        }
      }
    }

    return {
      totalContacts: contacts.length,
      contactsWithoutConversations: contactsWithoutConversations.length,
      queueItemsMarkedWithConversations: queueMismatches.length,
      actuallyHaveConversations: actuallyHaveConversations.length,
      exampleMismatches: contactsWithoutConversations.slice(0, 3)
    };
  });

  // Test 6: System Recovery Test (Simulation)
  await runTest(results, 'System Recovery Capability', async () => {
    // Simulate stuck processing detection
    const stuckItems = await prisma.followerProcessingQueue.count({
      where: {
        organizationId,
        status: 'processing',
        updatedAt: { lt: new Date(Date.now() - 300000) }
      }
    });

    // Check if watchdog would work
    const unprocessedConversations = await prisma.instagramConversationsNotGathered.count({
      where: { organizationId, isGathered: false }
    });

    // Check recent queue activity
    const recentActivity = await prisma.followerProcessingQueue.findMany({
      where: {
        organizationId,
        updatedAt: { gt: new Date(Date.now() - 300000) } // Last 5 minutes
      },
      orderBy: { updatedAt: 'desc' },
      take: 5
    });

    return {
      stuckItems,
      unprocessedConversations,
      recentActivity: recentActivity.length,
      lastActivity: recentActivity[0]?.updatedAt || null,
      recoveryRecommendation: stuckItems > 0 || unprocessedConversations > 0 ? 
        'System needs recovery action' : 'System appears healthy'
    };
  });

  return results;
}

async function runTest(results: any, testName: string, testFunction: () => Promise<any>) {
  const test = {
    name: testName,
    status: 'running' as string,
    startTime: new Date().toISOString(),
    endTime: null as string | null,
    duration: 0,
    result: null as any,
    error: null as string | null,
    warnings: [] as string[]
  };

  results.tests.push(test);
  results.summary.total++;

  try {
    console.log(`🧪 Running test: ${testName}`);
    const startTime = Date.now();
    
    test.result = await testFunction();
    
    const endTime = Date.now();
    test.duration = endTime - startTime;
    test.endTime = new Date().toISOString();
    test.status = 'passed';
    
    // Add warnings based on test results
    if (testName === 'Queue Processing Health' && test.result?.stuckItems > 0) {
      test.warnings.push(`${test.result.stuckItems} items stuck in processing state`);
      results.summary.warnings++;
    }
    
    if (testName === 'Conversation Processing Stats' && test.result?.isStuck) {
      test.warnings.push('Conversation processing appears to be stuck');
      results.summary.warnings++;
    }
    
    if (testName === 'Data Consistency Check' && test.result?.contactsWithoutConversations > 0) {
      test.warnings.push(`${test.result.contactsWithoutConversations} contacts without conversations`);
      results.summary.warnings++;
    }

    results.summary.passed++;
    console.log(`✅ Test passed: ${testName} (${test.duration}ms)`);
    
  } catch (error) {
    test.status = 'failed';
    test.error = error instanceof Error ? error.message : 'Unknown error';
    test.endTime = new Date().toISOString();
    test.duration = Date.now() - new Date(test.startTime).getTime();
    
    results.summary.failed++;
    console.error(`❌ Test failed: ${testName} - ${test.error}`);
  }
}

export async function GET(req: NextRequest): Promise<Response> {
  return NextResponse.json({
    success: true,
    description: 'Comprehensive conversation gathering test suite',
    tests: [
      'Configuration Check - Verify Instagram token and extension settings',
      'Conversation Data Integrity - Check for duplicates and orphaned data',
      'Queue Processing Health - Analyze queue items and stuck processing',
      'Conversation Processing Stats - Get current processing statistics',
      'Data Consistency Check - Verify data relationships',
      'System Recovery Capability - Test recovery mechanisms'
    ],
    usage: {
      method: 'POST',
      description: 'Run all tests for the conversation gathering system'
    }
  });
}