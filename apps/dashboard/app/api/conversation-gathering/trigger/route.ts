import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';
import { conversationProcessor } from '~/lib/conversation-processor';
import { followerProcessingQueue } from '~/lib/follower-processing-queue';

export async function POST(req: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: { userId: session.user.id }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const organizationId = membership.organizationId;
    const body = await req.json();
    const action = body.action || 'process_conversations';

    console.log(`🚀 Manual trigger requested for organization ${organizationId}: ${action}`);

    let result: any = {};

    switch (action) {
      case 'process_conversations':
        console.log('🔄 Processing stuck conversations...');
        result = await conversationProcessor.processOrganizationConversations(organizationId);
        break;

      case 'cleanup_stuck':
        console.log('🧹 Cleaning up stuck processing items...');
        const cleanedCount = await conversationProcessor.cleanupStuckProcessing(organizationId);
        result = { success: true, cleanedItems: cleanedCount };
        break;

      case 'restart_queue':
        console.log('🔄 Restarting queue processing...');
        await followerProcessingQueue.stop();
        await followerProcessingQueue.start();
        result = { success: true, message: 'Queue restarted' };
        break;

      case 'retry_failed':
        console.log('🔄 Retrying failed queue items...');
        const retriedCount = await followerProcessingQueue.retryFailed(organizationId);
        result = { success: true, retriedItems: retriedCount };
        break;

      case 'force_complete':
        console.log('⚠️ Force completing conversation gathering...');
        // Mark all remaining conversations as gathered (emergency measure)
        const forceResult = await prisma.instagramConversationsNotGathered.updateMany({
          where: { organizationId, isGathered: false },
          data: { isGathered: true }
        });
        
        // Update extension status
        await prisma.chromeExtensionSettings.update({
          where: { organizationId },
          data: {
            extensionStatus: 'CONVERSATIONS_GATHERED_READY',
            currentActivity: 'Force completed - ready for attack list creation',
            lastActivityAt: new Date()
          }
        });

        result = { 
          success: true, 
          message: 'Force completed conversation gathering',
          markedAsGathered: forceResult.count
        };
        break;

      case 'full_reset':
        console.log('🔄 Performing full reset...');
        // Reset all failed queue items
        await prisma.followerProcessingQueue.updateMany({
          where: { organizationId, status: { in: ['failed', 'processing'] } },
          data: { status: 'pending', attempts: 0, errorMessage: null, updatedAt: new Date() }
        });

        // Process conversations
        const processResult = await conversationProcessor.processOrganizationConversations(organizationId);
        
        // Restart queue
        await followerProcessingQueue.stop();
        await followerProcessingQueue.start();

        result = {
          success: true,
          message: 'Full reset completed',
          conversationProcessing: processResult
        };
        break;

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }

    // Get updated stats
    const updatedStats = await conversationProcessor.getProcessingStats(organizationId);

    return NextResponse.json({
      success: true,
      action,
      result,
      updatedStats,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in manual conversation gathering trigger:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(req: NextRequest): Promise<Response> {
  return NextResponse.json({
    success: true,
    availableActions: [
      {
        action: 'process_conversations',
        description: 'Process all unprocessed conversations with AI analysis'
      },
      {
        action: 'cleanup_stuck',
        description: 'Clean up items stuck in processing state'
      },
      {
        action: 'restart_queue',
        description: 'Restart the follower processing queue'
      },
      {
        action: 'retry_failed',
        description: 'Retry all failed queue items'
      },
      {
        action: 'force_complete',
        description: 'Force mark all conversations as gathered (emergency use only)'
      },
      {
        action: 'full_reset',
        description: 'Full system reset - clean up, process, and restart everything'
      }
    ],
    usage: {
      method: 'POST',
      body: { action: 'action_name' }
    }
  });
}