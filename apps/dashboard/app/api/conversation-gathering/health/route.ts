import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';
import { conversationProcessor } from '~/lib/conversation-processor';
import { followerProcessingQueue } from '~/lib/follower-processing-queue';

export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: { userId: session.user.id }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const organizationId = membership.organizationId;

    // Comprehensive health check
    const healthCheck = await performHealthCheck(organizationId);

    return NextResponse.json({
      success: true,
      organizationId,
      timestamp: new Date().toISOString(),
      health: healthCheck
    });

  } catch (error) {
    console.error('Error in conversation gathering health check:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function performHealthCheck(organizationId: string) {
  const [
    conversationStats,
    queueStats,
    stuckItems,
    instagramSettings,
    extensionSettings,
    recentActivity
  ] = await Promise.all([
    // Conversation processing stats
    conversationProcessor.getProcessingStats(organizationId),
    
    // Queue stats
    followerProcessingQueue.getStats(),
    
    // Stuck processing items
    prisma.followerProcessingQueue.findMany({
      where: {
        organizationId,
        status: 'processing',
        updatedAt: { lt: new Date(Date.now() - 300000) } // 5 minutes ago
      },
      include: { InstagramFollower: true }
    }),
    
    // Instagram settings
    prisma.instagramSettings.findFirst({
      where: { organizationId, instagramToken: { not: null } }
    }),
    
    // Extension settings
    prisma.chromeExtensionSettings.findUnique({
      where: { organizationId }
    }),
    
    // Recent activity
    prisma.instagramConversationsNotGathered.findMany({
      where: { organizationId, isGathered: true },
      orderBy: { updatedAt: 'desc' },
      take: 5,
      select: { participantUsername: true, updatedAt: true }
    })
  ]);

  // Health assessment
  const issues = [];
  let healthScore = 100;

  // Check conversation processing
  if (conversationStats.totalConversations > 0 && conversationStats.gatheredConversations === 0) {
    issues.push('No conversations have been processed');
    healthScore -= 30;
  }

  if (conversationStats.pendingConversations > 0 && conversationStats.pendingConversations > 5) {
    issues.push(`${conversationStats.pendingConversations} conversations pending processing`);
    healthScore -= 20;
  }

  // Check for stuck items
  if (stuckItems.length > 0) {
    issues.push(`${stuckItems.length} items stuck in processing state`);
    healthScore -= 25;
  }

  // Check Instagram API configuration
  if (!instagramSettings?.instagramToken) {
    issues.push('Instagram API token not configured');
    healthScore -= 50;
  }

  // Check extension status
  if (extensionSettings?.extensionStatus === 'CONVERSATIONS_GATHERING' && 
      conversationStats.percentComplete < 100) {
    const timeSinceLastActivity = recentActivity.length > 0 
      ? Date.now() - new Date(recentActivity[0].updatedAt).getTime()
      : Date.now();
    
    if (timeSinceLastActivity > 600000) { // 10 minutes
      issues.push('Conversation gathering appears stuck (no activity for 10+ minutes)');
      healthScore -= 40;
    }
  }

  // Determine health status
  let status = 'healthy';
  if (healthScore < 50) status = 'critical';
  else if (healthScore < 80) status = 'warning';

  return {
    status,
    score: Math.max(0, healthScore),
    issues,
    details: {
      conversations: conversationStats,
      queue: queueStats,
      stuckItems: stuckItems.map(item => ({
        id: item.id,
        follower: item.InstagramFollower?.instagramNickname,
        stuckSince: item.updatedAt,
        attempts: item.attempts
      })),
      configuration: {
        hasInstagramToken: !!instagramSettings?.instagramToken,
        extensionStatus: extensionSettings?.extensionStatus,
        extensionConnected: extensionSettings?.isConnected
      },
      recentActivity: recentActivity.map(activity => ({
        username: activity.participantUsername,
        processedAt: activity.updatedAt
      }))
    },
    recommendations: generateRecommendations(issues, conversationStats, stuckItems.length)
  };
}

function generateRecommendations(issues: string[], stats: any, stuckCount: number): string[] {
  const recommendations = [];

  if (issues.includes('No conversations have been processed')) {
    recommendations.push('Check Instagram API token and trigger manual conversation processing');
  }

  if (stats.pendingConversations > 5) {
    recommendations.push('Consider triggering manual conversation processing to clear backlog');
  }

  if (stuckCount > 0) {
    recommendations.push('Reset stuck processing items and restart queue processing');
  }

  if (issues.includes('Instagram API token not configured')) {
    recommendations.push('Configure Instagram API token in settings');
  }

  if (issues.includes('Conversation gathering appears stuck')) {
    recommendations.push('Restart conversation gathering process or check for API rate limits');
  }

  if (recommendations.length === 0) {
    recommendations.push('System appears healthy - no action needed');
  }

  return recommendations;
}