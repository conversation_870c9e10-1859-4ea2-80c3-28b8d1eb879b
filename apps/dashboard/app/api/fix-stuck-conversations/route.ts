import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';
import { conversationProcessor } from '~/lib/conversation-processor';
import { followerProcessingQueue } from '~/lib/follower-processing-queue';

/**
 * Emergency fix endpoint for stuck conversation gathering
 * This endpoint will diagnose and fix the current stuck state
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: { userId: session.user.id }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const organizationId = membership.organizationId;
    
    console.log(`🚨 EMERGENCY FIX: Starting stuck conversation gathering fix for organization: ${organizationId}`);

    // Step 1: Diagnose the current state
    const diagnostics = await diagnoseProblem(organizationId);
    console.log('🔍 Diagnostics:', diagnostics);

    // Step 2: Apply fixes based on diagnosis
    const fixes = await applyFixes(organizationId, diagnostics);
    console.log('🔧 Applied fixes:', fixes);

    // Step 3: Verify the fix worked
    const verification = await verifyFix(organizationId);
    console.log('✅ Verification:', verification);

    return NextResponse.json({
      success: true,
      organizationId,
      diagnostics,
      fixes,
      verification,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error in emergency conversation fix:', error);
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

async function diagnoseProblem(organizationId: string) {
  const [
    conversations,
    queueItems,
    extensionSettings,
    instagramSettings
  ] = await Promise.all([
    prisma.instagramConversationsNotGathered.findMany({
      where: { organizationId }
    }),
    prisma.followerProcessingQueue.findMany({
      where: { organizationId },
      include: { InstagramFollower: true }
    }),
    prisma.chromeExtensionSettings.findUnique({
      where: { organizationId }
    }),
    prisma.instagramSettings.findFirst({
      where: { organizationId, instagramToken: { not: null } }
    })
  ]);

  const gathered = conversations.filter(c => c.isGathered);
  const notGathered = conversations.filter(c => !c.isGathered);
  const pendingWithConversations = queueItems.filter(i => i.status === 'pending' && i.hasConversation);
  const stuckInProcessing = queueItems.filter(i => 
    i.status === 'processing' && 
    Date.now() - new Date(i.updatedAt).getTime() > 300000
  );

  return {
    totalConversations: conversations.length,
    gatheredConversations: gathered.length,
    notGatheredConversations: notGathered.length,
    pendingQueueItems: pendingWithConversations.length,
    stuckProcessingItems: stuckInProcessing.length,
    extensionStatus: extensionSettings?.extensionStatus,
    hasInstagramToken: !!instagramSettings?.instagramToken,
    problems: {
      hasStuckConversations: notGathered.length > 0,
      hasStuckProcessing: stuckInProcessing.length > 0,
      hasPendingQueue: pendingWithConversations.length > 0,
      missingConfiguration: !instagramSettings?.instagramToken
    }
  };
}

async function applyFixes(organizationId: string, diagnostics: any) {
  const fixes = {
    cleanedStuckItems: 0,
    processedConversations: { processed: 0, failed: 0 },
    retriedQueueItems: 0,
    updatedExtensionStatus: false,
    errors: [] as string[]
  };

  try {
    // Fix 1: Clean up stuck processing items
    if (diagnostics.problems.hasStuckProcessing) {
      console.log('🧹 Cleaning up stuck processing items...');
      fixes.cleanedStuckItems = await conversationProcessor.cleanupStuckProcessing(organizationId);
    }

    // Fix 2: Process stuck conversations with robust processor
    if (diagnostics.problems.hasStuckConversations) {
      console.log('🔄 Processing stuck conversations...');
      try {
        const result = await conversationProcessor.processOrganizationConversations(organizationId);
        fixes.processedConversations = {
          processed: result.processed,
          failed: result.failed
        };
        if (result.errors.length > 0) {
          fixes.errors.push(...result.errors);
        }
      } catch (error) {
        fixes.errors.push(`Conversation processing failed: ${error}`);
      }
    }

    // Fix 3: Retry failed queue items
    if (diagnostics.problems.hasPendingQueue) {
      console.log('🔄 Retrying failed queue items...');
      try {
        fixes.retriedQueueItems = await followerProcessingQueue.retryFailed(organizationId);
      } catch (error) {
        fixes.errors.push(`Queue retry failed: ${error}`);
      }
    }

    // Fix 4: Force queue processing if everything else is fixed
    if (diagnostics.problems.hasStuckConversations || diagnostics.problems.hasStuckProcessing) {
      console.log('🚀 Triggering queue processing...');
      try {
        // Don't await this - let it run in background
        followerProcessingQueue.processQueue().catch(error => {
          console.error('Background queue processing error:', error);
        });
      } catch (error) {
        fixes.errors.push(`Queue trigger failed: ${error}`);
      }
    }

    // Fix 5: Update extension status if needed
    const updatedStats = await conversationProcessor.getProcessingStats(organizationId);
    if (updatedStats.isComplete) {
      console.log('✅ Conversation gathering is complete - updating extension status');
      try {
        await prisma.chromeExtensionSettings.update({
          where: { organizationId },
          data: {
            extensionStatus: 'CONVERSATIONS_GATHERED_READY',
            currentActivity: 'All conversations processed - ready for attack list creation',
            lastActivityAt: new Date()
          }
        });
        fixes.updatedExtensionStatus = true;
      } catch (error) {
        fixes.errors.push(`Extension status update failed: ${error}`);
      }
    }

  } catch (error) {
    fixes.errors.push(`General fix error: ${error}`);
  }

  return fixes;
}

async function verifyFix(organizationId: string) {
  // Wait a moment for async operations to complete
  await new Promise(resolve => setTimeout(resolve, 2000));

  const stats = await conversationProcessor.getProcessingStats(organizationId);
  
  const stuckItems = await prisma.followerProcessingQueue.count({
    where: {
      organizationId,
      status: 'processing',
      updatedAt: { lt: new Date(Date.now() - 300000) }
    }
  });

  const extensionSettings = await prisma.chromeExtensionSettings.findUnique({
    where: { organizationId }
  });

  return {
    conversationStats: stats,
    stuckProcessingItems: stuckItems,
    extensionStatus: extensionSettings?.extensionStatus,
    isFixed: stats.isComplete && stuckItems === 0,
    nextSteps: generateNextSteps(stats, stuckItems, extensionSettings?.extensionStatus)
  };
}

function generateNextSteps(stats: any, stuckItems: number, extensionStatus: string | undefined): string[] {
  const steps = [];

  if (!stats.isComplete) {
    if (stats.pendingConversations > 0) {
      steps.push(`Wait for ${stats.pendingConversations} conversations to finish processing`);
    }
    if (stats.pendingQueueItems > 0) {
      steps.push(`Wait for ${stats.pendingQueueItems} queue items to process`);
    }
  }

  if (stuckItems > 0) {
    steps.push('Re-run this fix endpoint to clean up remaining stuck items');
  }

  if (stats.isComplete && extensionStatus !== 'CONVERSATIONS_GATHERED_READY') {
    steps.push('Extension status should automatically update to CONVERSATIONS_GATHERED_READY');
  }

  if (stats.isComplete && extensionStatus === 'CONVERSATIONS_GATHERED_READY') {
    steps.push('✅ System is ready - Chrome extension can proceed with attack list creation');
  }

  if (steps.length === 0) {
    steps.push('System appears healthy - monitor for a few minutes to ensure stability');
  }

  return steps;
}

export async function GET(req: NextRequest): Promise<Response> {
  return NextResponse.json({
    success: true,
    description: 'Emergency fix endpoint for stuck conversation gathering',
    whatItDoes: [
      'Diagnoses current conversation gathering problems',
      'Cleans up stuck processing items',
      'Processes unprocessed conversations with robust error handling',
      'Retries failed queue items',
      'Updates extension status when complete',
      'Provides verification and next steps'
    ],
    usage: {
      method: 'POST',
      description: 'Run emergency fix for stuck conversation gathering'
    }
  });
}