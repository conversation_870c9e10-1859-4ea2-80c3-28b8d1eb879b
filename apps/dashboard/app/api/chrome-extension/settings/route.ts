import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { auth } from '@workspace/auth';
import { verifyApi<PERSON>ey } from '@workspace/api-keys';
import { prisma } from '@workspace/database/client';

// CORS headers for Chrome Extension
function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key, ngrok-skip-browser-warning',
    'Access-Control-Max-Age': '86400',
  };
}

// Handle preflight OPTIONS requests
export async function OPTIONS(req: NextRequest): Promise<Response> {
  return new NextResponse(null, {
    status: 200,
    headers: getCorsHeaders(),
  });
}

const UpdateSettingsSchema = z.object({
  timeBetweenDMsMin: z.number().min(1).max(99),
  timeBetweenDMsMax: z.number().min(1).max(99),
  messagesBeforeBreakMin: z.number().min(1).max(99),
  messagesBeforeBreakMax: z.number().min(1).max(99),
  breakDurationMin: z.number().min(1).max(99),
  breakDurationMax: z.number().min(1).max(99),
  pauseStart: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format (HH:MM)"),
  pauseStop: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format (HH:MM)"),
  smartFocus: z.boolean(),
  // Message limit settings
  dailyMessageLimit: z.number().min(10).nullable().optional(), // null = unlimited, min 10
  attackPastFollowers: z.boolean().optional(),
  continuousScraping: z.boolean().optional(),
  // Scraping settings
  scrapingEnabled: z.boolean().optional(),
  scrapingIntervalMin: z.number().min(5).max(1440).optional(), // 5 minutes to 24 hours
  scrapingIntervalMax: z.number().min(5).max(1440).optional(), // 5 minutes to 24 hours
  // System fields
  isConnected: z.boolean().optional(),
  extensionStatus: z.string().optional().superRefine((status, ctx) => {
    const baseStatuses = ['FRESH_START', 'ACTIVE', 'IDLE', 'STOPPED', 'ALL_SCRAPED', 'CONVERSATIONS_GATHERING', 'CONVERSATIONS_GATHERING_PAUSED', 'CONVERSATIONS_GATHERING_COMPLETED', 'CONVERSATIONS_GATHERED_READY'];
    const isValid =
      baseStatuses.includes(status!) ||
      /^SCRAPED_\d+$/.test(status!);
  
    if (!isValid) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: `Invalid extension status: "${status}"`,
      });
    }
  }).optional(),
  currentActivity: z.string().optional(),
}).refine((data) => {
  return data.timeBetweenDMsMin <= data.timeBetweenDMsMax &&
         data.messagesBeforeBreakMin <= data.messagesBeforeBreakMax &&
         data.breakDurationMin <= data.breakDurationMax &&
         (!data.scrapingIntervalMin || !data.scrapingIntervalMax || data.scrapingIntervalMin <= data.scrapingIntervalMax);
}, {
  message: "Minimum values must be less than or equal to maximum values",
});

// Helper function to check if daily counter needs reset
function needsDailyReset(lastResetDate: Date): boolean {
  const now = new Date();
  const lastReset = new Date(lastResetDate);
  
  // Reset if dates are different (new day)
  return now.toDateString() !== lastReset.toDateString();
}

// Helper function to calculate if continuous scraping is available
function canScrapeContinuously(lastContinuousScrapingAt: Date | null): boolean {
  if (!lastContinuousScrapingAt) return true;
  
  const now = new Date();
  const hoursSinceLastScraping = (now.getTime() - lastContinuousScrapingAt.getTime()) / (1000 * 60 * 60);
  
  return hoursSinceLastScraping >= 1; // 1 hour minimum
}

/**
 * Get Chrome Extension Settings
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check for API key first (Chrome extension)
    const apiKey = req.headers.get('X-API-Key');
    let organizationId: string;

    if (apiKey) {
      // API key authentication (Chrome extension)
      const result = await verifyApiKey(apiKey);
      if (!result.success) {
        return NextResponse.json(
          { success: false, error: result.errorMessage },
          { status: 401, headers: getCorsHeaders() }
        );
      }
      organizationId = result.organizationId;
    } else {
      // Session authentication (web dashboard)
      const session = await auth();
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401, headers: getCorsHeaders() }
        );
      }

      // Get user's organization
      const membership = await prisma.membership.findFirst({
        where: {
          userId: session.user.id
        }
      });

      if (!membership) {
        return NextResponse.json(
          { success: false, error: 'No organization found' },
          { status: 404, headers: getCorsHeaders() }
        );
      }
      organizationId = membership.organizationId;
    }

    // Get or create Chrome Extension settings
    let settings = await prisma.chromeExtensionSettings.findUnique({
      where: {
        organizationId: organizationId
      }
    });

    // Create default settings if they don't exist
    if (!settings) {
      settings = await prisma.chromeExtensionSettings.create({
        data: {
          organizationId: organizationId,
          timeBetweenDMsMin: 3,
          timeBetweenDMsMax: 8,
          messagesBeforeBreakMin: 8,
          messagesBeforeBreakMax: 15,
          breakDurationMin: 10,
          breakDurationMax: 20,
          pauseStart: "00:30",
          pauseStop: "07:00",
          smartFocus: true,
          dailyMessageLimit: null, // Unlimited by default
          messagesSentToday: 0,
          lastMessageResetDate: new Date(),
          attackPastFollowers: true,
          continuousScraping: true,
          scrapingEnabled: true,
          scrapingIntervalMin: 15,
          scrapingIntervalMax: 30,
          isConnected: false,
          extensionStatus: "FRESH_START",
        }
      });
    }

    // Check if daily counter needs reset
    if (needsDailyReset(settings.lastMessageResetDate)) {
      settings = await prisma.chromeExtensionSettings.update({
        where: { organizationId },
        data: {
          messagesSentToday: 0,
          lastMessageResetDate: new Date()
        }
      });
    }

    // Calculate remaining messages for today
    const remainingMessages = settings.dailyMessageLimit
      ? Math.max(0, settings.dailyMessageLimit - settings.messagesSentToday)
      : null;

    // Check if continuous scraping is available
    const canContinuousScrape = canScrapeContinuously(settings.lastContinuousScrapingAt);
    const nextContinuousScrapingAt = settings.lastContinuousScrapingAt && !canContinuousScrape
      ? new Date(settings.lastContinuousScrapingAt.getTime() + 60 * 60 * 1000) // 1 hour after last scraping
      : null;

    return NextResponse.json({
      success: true,
      data: {
        timeBetweenDMsMin: settings.timeBetweenDMsMin,
        timeBetweenDMsMax: settings.timeBetweenDMsMax,
        messagesBeforeBreakMin: settings.messagesBeforeBreakMin,
        messagesBeforeBreakMax: settings.messagesBeforeBreakMax,
        breakDurationMin: settings.breakDurationMin,
        breakDurationMax: settings.breakDurationMax,
        pauseStart: settings.pauseStart,
        pauseStop: settings.pauseStop,
        smartFocus: settings.smartFocus,
        // Message limit settings
        dailyMessageLimit: settings.dailyMessageLimit,
        messagesSentToday: settings.messagesSentToday,
        lastMessageResetDate: settings.lastMessageResetDate?.toISOString(),
        canSendMoreMessages: !settings.dailyMessageLimit || settings.messagesSentToday < settings.dailyMessageLimit,
        remainingMessages,
        attackPastFollowers: settings.attackPastFollowers,
        continuousScraping: settings.continuousScraping,
        canScrapeContinuously: canContinuousScrape,
        nextContinuousScrapingAt: nextContinuousScrapingAt?.toISOString(),
        // Scraping settings
        scrapingEnabled: settings.scrapingEnabled,
        scrapingIntervalMin: settings.scrapingIntervalMin,
        scrapingIntervalMax: settings.scrapingIntervalMax,
        nextScrapingAllowedAt: settings.nextScrapingAllowedAt?.toISOString(),
        // System fields
        isConnected: settings.isConnected,
        lastConnectionAt: settings.lastConnectionAt,
        extensionStatus: settings.extensionStatus,
        currentActivity: settings.currentActivity,
        lastActivityAt: settings.lastActivityAt,
      }
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error getting Chrome Extension settings:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}

/**
 * Update Chrome Extension Settings
 */
export async function PUT(req: NextRequest): Promise<Response> {
  try {
    // Check for API key first (Chrome extension)
    const apiKey = req.headers.get('X-API-Key');
    let organizationId: string;

    if (apiKey) {
      // API key authentication (Chrome extension)
      const result = await verifyApiKey(apiKey);
      if (!result.success) {
        return NextResponse.json(
          { success: false, error: result.errorMessage },
          { status: 401, headers: getCorsHeaders() }
        );
      }
      organizationId = result.organizationId;
    } else {
      // Session authentication (web dashboard)
      const session = await auth();
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401, headers: getCorsHeaders() }
        );
      }

      // Get user's organization
      const membership = await prisma.membership.findFirst({
        where: {
          userId: session.user.id
        }
      });

      if (!membership) {
        return NextResponse.json(
          { success: false, error: 'No organization found' },
          { status: 404, headers: getCorsHeaders() }
        );
      }
      organizationId = membership.organizationId;
    }

    const body = await req.json();
    const validatedData = UpdateSettingsSchema.parse(body);

    // Update or create Chrome Extension settings
    const settings = await prisma.chromeExtensionSettings.upsert({
      where: {
        organizationId: organizationId
      },
      update: {
        timeBetweenDMsMin: validatedData.timeBetweenDMsMin,
        timeBetweenDMsMax: validatedData.timeBetweenDMsMax,
        messagesBeforeBreakMin: validatedData.messagesBeforeBreakMin,
        messagesBeforeBreakMax: validatedData.messagesBeforeBreakMax,
        breakDurationMin: validatedData.breakDurationMin,
        breakDurationMax: validatedData.breakDurationMax,
        pauseStart: validatedData.pauseStart,
        pauseStop: validatedData.pauseStop,
        smartFocus: validatedData.smartFocus,
        // Message limit settings
        ...(validatedData.dailyMessageLimit !== undefined && {
          dailyMessageLimit: validatedData.dailyMessageLimit,
        }),
        ...(validatedData.attackPastFollowers !== undefined && {
          attackPastFollowers: validatedData.attackPastFollowers,
        }),
        ...(validatedData.continuousScraping !== undefined && {
          continuousScraping: validatedData.continuousScraping,
        }),
        // Scraping settings
        ...(validatedData.scrapingEnabled !== undefined && {
          scrapingEnabled: validatedData.scrapingEnabled,
        }),
        ...(validatedData.scrapingIntervalMin !== undefined && {
          scrapingIntervalMin: validatedData.scrapingIntervalMin,
        }),
        ...(validatedData.scrapingIntervalMax !== undefined && {
          scrapingIntervalMax: validatedData.scrapingIntervalMax,
        }),
        // System fields
        ...(validatedData.isConnected !== undefined && {
          isConnected: validatedData.isConnected,
          lastConnectionAt: validatedData.isConnected ? new Date() : undefined,
        }),
        ...(validatedData.extensionStatus !== undefined && {
          extensionStatus: validatedData.extensionStatus === 'SCRAPED_250_FOLLOWERS' ? 'SCRAPED_250' : validatedData.extensionStatus,
          lastActivityAt: new Date(),
        }),
        ...(validatedData.currentActivity !== undefined && {
          currentActivity: validatedData.currentActivity,
          lastActivityAt: new Date(),
        }),
        updatedAt: new Date(),
      },
      create: {
        organizationId: organizationId,
        timeBetweenDMsMin: validatedData.timeBetweenDMsMin,
        timeBetweenDMsMax: validatedData.timeBetweenDMsMax,
        messagesBeforeBreakMin: validatedData.messagesBeforeBreakMin,
        messagesBeforeBreakMax: validatedData.messagesBeforeBreakMax,
        breakDurationMin: validatedData.breakDurationMin,
        breakDurationMax: validatedData.breakDurationMax,
        pauseStart: validatedData.pauseStart,
        pauseStop: validatedData.pauseStop,
        smartFocus: validatedData.smartFocus,
        // Message limit settings
        dailyMessageLimit: validatedData.dailyMessageLimit ?? null,
        messagesSentToday: 0,
        lastMessageResetDate: new Date(),
        attackPastFollowers: validatedData.attackPastFollowers ?? true,
        continuousScraping: validatedData.continuousScraping ?? true,
        // Scraping settings
        scrapingEnabled: validatedData.scrapingEnabled ?? true,
        scrapingIntervalMin: validatedData.scrapingIntervalMin ?? 15,
        scrapingIntervalMax: validatedData.scrapingIntervalMax ?? 30,
        // System fields
        isConnected: validatedData.isConnected ?? false,
        lastConnectionAt: validatedData.isConnected ? new Date() : undefined,
        extensionStatus: validatedData.extensionStatus === 'SCRAPED_250_FOLLOWERS' ? 'SCRAPED_250' : (validatedData.extensionStatus ?? "FRESH_START"),
        currentActivity: validatedData.currentActivity,
        lastActivityAt: validatedData.extensionStatus || validatedData.currentActivity ? new Date() : undefined,
      }
    });

    // Calculate remaining messages for response
    const remainingMessages = settings.dailyMessageLimit
      ? Math.max(0, settings.dailyMessageLimit - settings.messagesSentToday)
      : null;

    // Check if continuous scraping is available
    const canContinuousScrape = canScrapeContinuously(settings.lastContinuousScrapingAt);
    const nextContinuousScrapingAt = settings.lastContinuousScrapingAt && !canContinuousScrape
      ? new Date(settings.lastContinuousScrapingAt.getTime() + 60 * 60 * 1000)
      : null;

    return NextResponse.json({
      success: true,
      data: {
        timeBetweenDMsMin: settings.timeBetweenDMsMin,
        timeBetweenDMsMax: settings.timeBetweenDMsMax,
        messagesBeforeBreakMin: settings.messagesBeforeBreakMin,
        messagesBeforeBreakMax: settings.messagesBeforeBreakMax,
        breakDurationMin: settings.breakDurationMin,
        breakDurationMax: settings.breakDurationMax,
        pauseStart: settings.pauseStart,
        pauseStop: settings.pauseStop,
        smartFocus: settings.smartFocus,
        // Message limit settings
        dailyMessageLimit: settings.dailyMessageLimit,
        messagesSentToday: settings.messagesSentToday,
        lastMessageResetDate: settings.lastMessageResetDate?.toISOString(),
        canSendMoreMessages: !settings.dailyMessageLimit || settings.messagesSentToday < settings.dailyMessageLimit,
        remainingMessages,
        attackPastFollowers: settings.attackPastFollowers,
        continuousScraping: settings.continuousScraping,
        canScrapeContinuously: canContinuousScrape,
        nextContinuousScrapingAt: nextContinuousScrapingAt?.toISOString(),
        // Scraping settings
        scrapingEnabled: settings.scrapingEnabled,
        scrapingIntervalMin: settings.scrapingIntervalMin,
        scrapingIntervalMax: settings.scrapingIntervalMax,
        nextScrapingAllowedAt: settings.nextScrapingAllowedAt?.toISOString(),
        // System fields
        isConnected: settings.isConnected,
        lastConnectionAt: settings.lastConnectionAt,
        extensionStatus: settings.extensionStatus,
        currentActivity: settings.currentActivity,
        lastActivityAt: settings.lastActivityAt,
      }
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error updating Chrome Extension settings:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}
