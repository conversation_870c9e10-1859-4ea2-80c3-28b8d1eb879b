import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import crypto from 'crypto';

import { verifyApi<PERSON><PERSON> } from '@workspace/api-keys';
import { prisma } from '@workspace/database/client';

// CORS headers for Chrome Extension
function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key, ngrok-skip-browser-warning',
    'Access-Control-Max-Age': '86400',
  };
}

// Handle preflight OPTIONS requests
export async function OPTIONS(req: NextRequest): Promise<Response> {
  return new NextResponse(null, {
    status: 200,
    headers: getCorsHeaders(),
  });
}

const MarkSentSchema = z.object({
  contactId: z.string(),
  messageType: z.enum(['batch_sequence', 'follow_up', 'initial']),
  batchCompleted: z.boolean().optional(),
  sequenceNumber: z.number().optional(),
  messageHash: z.string().optional(),
  messageContent: z.string().optional(), // For generating hash if not provided
});

// Helper function to generate SHA256 hash
function generateMessageHash(message: string): string {
  return crypto.createHash('sha256').update(message).digest('hex');
}

/**
 * Mark message as sent and update counters
 * - Batch messages count as 1 message total (regardless of messages in batch)
 * - Follow-ups count as 1 message each
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    // Check for API key (Chrome extension)
    const apiKey = req.headers.get('X-API-Key');
    if (!apiKey) {
      return NextResponse.json(
        { success: false, error: 'API key required' },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    // Verify API key
    const result = await verifyApiKey(apiKey);
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.errorMessage },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    const organizationId = result.organizationId;
    const body = await req.json();
    const validatedData = MarkSentSchema.parse(body);

    // Get the contact
    const contact = await prisma.instagramContact.findUnique({
      where: { id: validatedData.contactId },
    });

    if (!contact || contact.organizationId !== organizationId) {
      return NextResponse.json(
        { success: false, error: 'Contact not found' },
        { status: 404, headers: getCorsHeaders() }
      );
    }

    // Calculate message hash if content provided
    const messageHash = validatedData.messageHash || 
      (validatedData.messageContent ? generateMessageHash(validatedData.messageContent) : null);

    // Prepare update data for contact
    const contactUpdateData: any = {
      lastMessageSentAt: new Date(),
    };

    // Handle different message types
    let messageCountIncrement = 0;
    
    if (validatedData.messageType === 'batch_sequence') {
      // Batch messages count as 1 message total
      if (validatedData.batchCompleted || validatedData.sequenceNumber === 1) {
        messageCountIncrement = 1;
        contactUpdateData.batchMessageStatus = 'sent';
        
        // Store first message hash for duplicate prevention
        if (messageHash && validatedData.sequenceNumber === 1) {
          contactUpdateData.firstMessageHash = messageHash;
        }
      }
      
      // Update current sequence number
      if (validatedData.sequenceNumber) {
        contactUpdateData.currentMessageSequence = validatedData.sequenceNumber;
      }
      
      // Update stage to 'initial' after first batch message
      if (contact.stage === 'new') {
        contactUpdateData.stage = 'initial';
      }
    } else if (validatedData.messageType === 'follow_up') {
      // Follow-ups count as 1 message each
      messageCountIncrement = 1;
      
      // Update stage if needed
      if (contact.stage === 'initial') {
        contactUpdateData.stage = 'engaged';
      }
    } else if (validatedData.messageType === 'initial') {
      // Single initial message counts as 1
      messageCountIncrement = 1;
      
      if (messageHash) {
        contactUpdateData.firstMessageHash = messageHash;
      }
      
      if (contact.stage === 'new') {
        contactUpdateData.stage = 'initial';
      }
    }

    // Update message hash tracking
    if (messageHash) {
      contactUpdateData.lastMessageSentHash = messageHash;
    }

    // Update contact's message count
    contactUpdateData.messagesSentCount = contact.messagesSentCount + messageCountIncrement;

    // Update contact
    await prisma.instagramContact.update({
      where: { id: validatedData.contactId },
      data: contactUpdateData,
    });

    // Update Chrome Extension daily counter
    if (messageCountIncrement > 0) {
      const settings = await prisma.chromeExtensionSettings.findUnique({
        where: { organizationId },
      });

      if (settings) {
        // Check if we need to reset the daily counter
        const now = new Date();
        const lastReset = new Date(settings.lastMessageResetDate);
        const needsReset = now.toDateString() !== lastReset.toDateString();

        await prisma.chromeExtensionSettings.update({
          where: { organizationId },
          data: {
            messagesSentToday: needsReset ? messageCountIncrement : settings.messagesSentToday + messageCountIncrement,
            lastMessageResetDate: needsReset ? now : settings.lastMessageResetDate,
            lastActivityAt: now,
            currentActivity: `Sent ${validatedData.messageType.replace('_', ' ')} to ${contact.instagramNickname}`,
          },
        });
      }
    }

    // Create stage change log if stage changed
    if (contactUpdateData.stage && contactUpdateData.stage !== contact.stage) {
      await prisma.stageChangeLog.create({
        data: {
          contactId: validatedData.contactId,
          fromStage: contact.stage,
          toStage: contactUpdateData.stage,
          reason: `${validatedData.messageType} message sent`,
          changedBy: 'chrome_extension',
          metadata: {
            messageType: validatedData.messageType,
            sequenceNumber: validatedData.sequenceNumber,
            messageHash,
          },
        },
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        contactId: validatedData.contactId,
        messageType: validatedData.messageType,
        messageCounted: messageCountIncrement > 0,
        newStage: contactUpdateData.stage || contact.stage,
        messagesSentCount: contact.messagesSentCount + messageCountIncrement,
      },
    }, {
      headers: getCorsHeaders(),
    });

  } catch (error) {
    console.error('Error marking message as sent:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data', details: error.errors },
        { status: 400, headers: getCorsHeaders() }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}
