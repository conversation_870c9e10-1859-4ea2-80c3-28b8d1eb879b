import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@workspace/database/client';
import { verifyApiKey } from '@workspace/api-keys';

// CORS headers for Chrome Extension
function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key, ngrok-skip-browser-warning',
    'Access-Control-Max-Age': '86400',
  };
}

// Handle preflight OPTIONS requests
export async function OPTIONS(req: NextRequest): Promise<Response> {
  return new NextResponse(null, {
    status: 200,
    headers: getCorsHeaders(),
  });
}

/**
 * Check conversation gathering status for Chrome extension
 * Returns status of conversation gathering process and progress
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check for API key (Chrome extension)
    const apiKey = req.headers.get('X-API-Key');
    if (!apiKey) {
      return NextResponse.json(
        { success: false, message: 'API key required' },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    // Verify API key and get organization
    const result = await verifyApiKey(apiKey);
    if (!result.success) {
      return NextResponse.json(
        { success: false, message: result.errorMessage },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    const organizationId = result.organizationId;

    // Get Chrome extension settings
    const settings = await prisma.chromeExtensionSettings.findUnique({
      where: { organizationId }
    });

    if (!settings) {
      return NextResponse.json(
        { success: false, message: 'Chrome extension settings not found' },
        { status: 404, headers: getCorsHeaders() }
      );
    }

    // NEW SIMPLE SYSTEM: Check follower processing status instead of old conversation gathering
    const totalFollowers = await prisma.followerProcessingQueue.count({
      where: { organizationId }
    });

    const processedFollowers = await prisma.followerProcessingQueue.count({
      where: { 
        organizationId,
        status: 'completed'
      }
    });

    const pendingFollowers = await prisma.followerProcessingQueue.count({
      where: {
        organizationId,
        status: 'pending'
      }
    });

    // Determine gathering status for new simple system
    const isGathering = settings.extensionStatus === 'CONVERSATIONS_GATHERING';
    const hasFollowers = totalFollowers > 0;
    const allProcessed = hasFollowers && processedFollowers === totalFollowers;
    const isComplete = hasFollowers && allProcessed && pendingFollowers === 0;

    // Auto-transition status when follower processing is complete
    if (isComplete && settings.extensionStatus === 'CONVERSATIONS_GATHERING') {
      console.log('🔄 SIMPLE_SYSTEM: Auto-transitioning status to CONVERSATIONS_GATHERED_READY');

      try {
        await prisma.chromeExtensionSettings.update({
          where: { organizationId },
          data: {
            extensionStatus: 'CONVERSATIONS_GATHERED_READY',
            currentActivity: 'All followers processed with simple system - ready for attack list creation',
            lastActivityAt: new Date()
          }
        });

        console.log('✅ SIMPLE_SYSTEM: Status auto-updated to CONVERSATIONS_GATHERED_READY');
      } catch (error) {
        console.error('❌ Error auto-updating extension status:', error);
      }
    }

    // Calculate progress for new simple system
    const percentComplete = totalFollowers > 0 
      ? Math.round((processedFollowers / totalFollowers) * 100)
      : 0;

    // Estimate time remaining (simple system is fast - seconds per follower)
    const remainingFollowers = totalFollowers - processedFollowers;
    const estimatedSeconds = remainingFollowers * 2; // 2 seconds per follower estimate
    const estimatedTimeRemaining = estimatedSeconds > 0 
      ? estimatedSeconds < 60 
        ? `${estimatedSeconds} second${estimatedSeconds !== 1 ? 's' : ''}`
        : `${Math.round(estimatedSeconds / 60)} minute${Math.round(estimatedSeconds / 60) !== 1 ? 's' : ''}`
      : null;

    // Get timing information for follower processing
    const firstFollower = await prisma.followerProcessingQueue.findFirst({
      where: { organizationId },
      orderBy: { createdAt: 'asc' }
    });

    const lastProcessedFollower = await prisma.followerProcessingQueue.findFirst({
      where: { 
        organizationId,
        status: 'completed'
      },
      orderBy: { updatedAt: 'desc' }
    });

    const response = {
      success: true,
      data: {
        isGathering,
        isComplete,
        progress: {
          totalContacts: totalFollowers,
          processedContacts: processedFollowers,
          percentComplete
        },
        estimatedTimeRemaining,
        startedAt: firstFollower?.createdAt || null,
        completedAt: isComplete ? (lastProcessedFollower?.updatedAt || new Date()) : null,
        // Additional status information
        extensionStatus: settings.extensionStatus,
        pendingConversationItems: pendingFollowers,
        hasConversations: hasFollowers
      }
    };

    console.log('🔄 SIMPLE_SYSTEM: Status check:', {
      isGathering,
      isComplete,
      totalFollowers,
      processedFollowers,
      percentComplete,
      pendingFollowers,
      extensionStatus: settings.extensionStatus
    });

    return NextResponse.json(response, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error checking conversation gathering status:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}
