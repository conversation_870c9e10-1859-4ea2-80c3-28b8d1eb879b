import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { auth } from '@workspace/auth';
import { verifyApiKey } from '@workspace/api-keys';
import { prisma } from '@workspace/database/client';
import { getAllConversations } from '~/lib/instagram-client';

/**
 * Enhanced status determination logic
 * Generates dynamic milestone statuses based on total scraped count
 */
function determineExtensionStatus(
  totalScraped: number,
  newFollowersCount: number,
  isComplete: boolean,
  reachedBottom: boolean,
): string {
  if (reachedBottom || isComplete) {
    return "ALL_SCRAPED";
  }

  const newTotal = totalScraped + newFollowersCount;

  // Generate milestone status (every 250 followers)
  const milestone = Math.floor(newTotal / 250) * 250;
  if (milestone > 0 && newTotal >= milestone) {
    return `SCRAPED_${milestone}`;
  }

  return "ACTIVE";
}

// CORS headers for Chrome Extension
function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key, ngrok-skip-browser-warning',
    'Access-Control-Max-Age': '86400',
  };
}

// Handle preflight OPTIONS requests
export async function OPTIONS(_req: NextRequest): Promise<Response> {
  return new NextResponse(null, {
    status: 200,
    headers: getCorsHeaders(),
  });
}

const ProcessFollowersSchema = z.object({
  followers: z.array(z.object({
    instagramNickname: z.string().min(1),
    instagramId: z.string().optional(),
    avatar: z.string().url().optional(),
    followerCount: z.number().int().min(0).optional(),
    isVerified: z.boolean().default(false)
  })).min(1).max(500),
  startPosition: z.number().int().min(0),
  totalFollowers: z.number().int().min(0).optional(),
  isComplete: z.boolean().optional(),
  // Enhanced tracking fields
  lastScrapedUsernames: z.array(z.string()).max(10).optional(),
});

/**
 * Process followers batch from Chrome Extension
 * Simply adds followers to InstagramFollower table
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    // Check for API key first (Chrome extension)
    const apiKey = req.headers.get('X-API-Key');
    let organizationId: string;
    let userId: string;

    if (apiKey) {
      // API key authentication (Chrome extension)
      const result = await verifyApiKey(apiKey);
      if (!result.success) {
        return NextResponse.json(
          { success: false, error: result.errorMessage },
          { status: 401, headers: getCorsHeaders() }
        );
      }
      organizationId = result.organizationId;

      // For API key auth, we need to get a user ID from the organization
      const membership = await prisma.membership.findFirst({
        where: {
          organizationId: result.organizationId,
          isOwner: true // Use the owner as the default user for API operations
        }
      });

      if (!membership) {
        return NextResponse.json(
          { success: false, error: 'No organization owner found' },
          { status: 404, headers: getCorsHeaders() }
        );
      }

      userId = membership.userId;
    } else {
      // Session authentication (web dashboard)
      const session = await auth();
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401, headers: getCorsHeaders() }
        );
      }

      // Get user's organization
      const membership = await prisma.membership.findFirst({
        where: {
          userId: session.user.id
        }
      });

      if (!membership) {
        return NextResponse.json(
          { success: false, error: 'No organization found' },
          { status: 404, headers: getCorsHeaders() }
        );
      }

      organizationId = membership.organizationId;
      userId = session.user.id;
    }



    const body = await req.json();
    console.log('🚨 DEBUG: Received request body:', JSON.stringify(body, null, 2));
    
    try {
      ProcessFollowersSchema.parse(body);
      console.log('🚨 DEBUG: Schema validation successful');
    } catch (validationError: unknown) {
      console.error('🚨 DEBUG: Schema validation failed:', validationError);
      const errorMessage = validationError instanceof Error ? validationError.message : 'Unknown validation error';
      return NextResponse.json(
        { success: false, error: `Validation error: ${errorMessage}` },
        { status: 400, headers: getCorsHeaders() }
      );
    }
    
    const validatedData = ProcessFollowersSchema.parse(body);

    // Process followers - simply add them to InstagramFollower table
    const results = {
      processed: 0,
      newFollowers: 0,
      existingFollowers: 0,
      errors: [] as string[]
    };

    for (const follower of validatedData.followers) {
      try {
        results.processed++;

        // Check if follower already exists in InstagramFollower table
        const existingFollower = await prisma.instagramFollower.findFirst({
          where: {
            organizationId,
            instagramNickname: follower.instagramNickname
          }
        });

        if (existingFollower) {
          results.existingFollowers++;
          continue;
        }

        // Add follower to InstagramFollower table
        const newFollower = await prisma.instagramFollower.create({
          data: {
            organizationId,
            userId: userId,
            instagramId: follower.instagramId,
            instagramNickname: follower.instagramNickname,
            avatar: follower.avatar,
            followerCount: follower.followerCount,
            isVerified: follower.isVerified,
            batchNumber: Math.floor(validatedData.startPosition / 500) + 1,
            priority: 'normal',
            status: 'pending',
            isTargeted: false,
            automationEnabled: true
          }
        });

        results.newFollowers++;

        console.log(`✅ Added ${follower.instagramNickname} to followers table`);

      } catch (error) {
        console.error(`Error processing follower ${follower.instagramNickname}:`, error);
        results.errors.push(`Failed to process ${follower.instagramNickname}: ${error}`);
      }
    }

    // Step 2: Update processing position
    await prisma.instagramFollowerProcessingState.upsert({
      where: {
        organizationId
      },
      update: {
        lastProcessedPosition: validatedData.startPosition + validatedData.followers.length,
        totalFollowersCount: validatedData.totalFollowers,
        lastProcessedAt: new Date(),
        updatedAt: new Date()
      },
      create: {
        organizationId,
        lastProcessedPosition: validatedData.startPosition + validatedData.followers.length,
        totalFollowersCount: validatedData.totalFollowers,
        lastProcessedAt: new Date()
      }
    });

    // Step 3: Enhanced status tracking - update ChromeExtensionSettings with new status
    const currentSettings = await prisma.chromeExtensionSettings.findUnique({
      where: { organizationId }
    });

    const currentTotalScraped = currentSettings?.totalFollowersScraped || 0;
    const newTotalScraped = currentTotalScraped + results.newFollowers;
    const isComplete = validatedData.isComplete || false;

    // Determine new status based on enhanced logic
    const newStatus = determineExtensionStatus(
      currentTotalScraped,
      results.newFollowers,
      isComplete,
      isComplete
    );

    // Prepare last scraped usernames (keep last 10)
    const newUsernames = validatedData.followers.map(f => f.instagramNickname);
    const existingUsernames = validatedData.lastScrapedUsernames || currentSettings?.lastScrapedUsernames || [];
    const combinedUsernames = [...existingUsernames, ...newUsernames];
    const lastScrapedUsernames = combinedUsernames.slice(-10); // Keep only last 10

    // 🚨 SET NEXT SCRAPING ALLOWED TIME: When 250 followers are reached, set 5-day wait
    const scrapingIntervalDays = currentSettings?.scrapingIntervalDays || 5;
    let nextScrapingAllowedAt: Date | null = null;

    // Check if we just completed a batch of 250 followers
    // Logic: if we uploaded 250+ followers in this batch, trigger the wait
    const reachedMilestone = results.newFollowers >= 250;

    if (reachedMilestone || isComplete) {
      // Set next allowed scraping time to 5 days from now
      nextScrapingAllowedAt = new Date();
      nextScrapingAllowedAt.setDate(nextScrapingAllowedAt.getDate() + scrapingIntervalDays);
      console.log(`🚨 Batch of 250 completed! Next scraping allowed at: ${nextScrapingAllowedAt.toISOString()}`);
    }

    // Update ChromeExtensionSettings with enhanced tracking
    await prisma.chromeExtensionSettings.upsert({
      where: { organizationId },
      update: {
        extensionStatus: newStatus,
        totalFollowersScraped: newTotalScraped,
        lastScrapedPosition: validatedData.startPosition + validatedData.followers.length,
        lastScrapedUsernames: lastScrapedUsernames,
        scrapingTargetReached: newTotalScraped >= 250 && (newTotalScraped % 250 === 0),
        allFollowersScraped: isComplete,
        lastScrapingSession: new Date(),
        nextScrapingAllowedAt: nextScrapingAllowedAt, // Set 5-day wait when batch completed
        currentActivity: isComplete ?
          `All ${newTotalScraped} followers processed` :
          `Processed ${results.newFollowers} followers (total: ${newTotalScraped})`,
        lastActivityAt: new Date(),
        updatedAt: new Date(),
      },
      create: {
        organizationId,
        extensionStatus: newStatus,
        totalFollowersScraped: newTotalScraped,
        lastScrapedPosition: validatedData.startPosition + validatedData.followers.length,
        lastScrapedUsernames: lastScrapedUsernames,
        scrapingTargetReached: newTotalScraped >= 250 && (newTotalScraped % 250 === 0),
        allFollowersScraped: isComplete,
        lastScrapingSession: new Date(),
        nextScrapingAllowedAt: nextScrapingAllowedAt, // Set 5-day wait when batch completed
        currentActivity: isComplete ?
          `All ${newTotalScraped} followers processed` :
          `Processed ${results.newFollowers} followers (total: ${newTotalScraped})`,
        lastActivityAt: new Date(),
        // Default settings
        timeBetweenDMsMin: 3,
        timeBetweenDMsMax: 8,
        messagesBeforeBreakMin: 8,
        messagesBeforeBreakMax: 15,
        breakDurationMin: 10,
        breakDurationMax: 20,
        pauseStart: "00:30",
        pauseStop: "07:00",
        smartFocus: true,
        isConnected: true,
        lastConnectionAt: new Date(),
      }
    });

    console.log(`✅ Enhanced status tracking: ${newStatus} (${newTotalScraped} total followers)`);
    console.log(`✅ Queued ${results.newFollowers} new followers for processing`);

    // 🚀 SIMPLE PROCESSING: Check if conversations gathered → Gather if needed → Match → Process
    if (results.newFollowers > 0) {
      console.log(`🚀 Processing ${results.newFollowers} new followers with simple flow...`);
      
      try {
        // Get Instagram settings
        const instagramSettings = await prisma.instagramSettings.findFirst({
          where: {
            organizationId,
            instagramToken: { not: null }
          }
        });

        // STEP 1: Check if InstagramConversationsNotGathered table is populated
        console.log(`Checking if conversation gathering has been done for organization: ${organizationId}`);
        const existingConversations = await prisma.instagramConversationsNotGathered.count({
          where: { organizationId }
        });

        // If no conversations in table, this is FIRST TIME - run conversation gathering
        if (existingConversations === 0 && instagramSettings?.instagramToken) {
          console.log(`🚀 FIRST FOLLOWER DETECTED - Running conversation gathering for organization: ${organizationId}`);
          
          try {
            // Get ALL conversations from Instagram API
            const instagramApiResponse = await getAllConversations(instagramSettings.instagramToken);
            const instagramApiConversations = instagramApiResponse.data || [];
            
            console.log(`Found ${instagramApiConversations.length} conversations to populate`);
            
            // Populate InstagramConversationsNotGathered table
            for (const conversation of instagramApiConversations) {
              const participants = conversation.participants || [];
              if (participants.length >= 2) {
                // Skip the first participant (business account) and take the second one
                const participant = participants[1];
                if (participant.username) {
                  try {
                    await prisma.instagramConversationsNotGathered.create({
                      data: {
                        organizationId,
                        instagramConversationId: conversation.id,
                        participantUsername: participant.username,
                        participantId: participant.id,
                        updatedTime: new Date(conversation.updated_time),
                        isGathered: false
                      }
                    });
                    console.log(`✅ Added conversation for: ${participant.username}`);
                  } catch (error) {
                    // Ignore duplicates
                    if (!(error instanceof Error) || !error.message?.includes('unique constraint')) {
                      console.error(`Error adding conversation for ${participant.username}:`, error);
                    }
                  }
                }
              }
            }
            
            console.log(`✅ Conversation gathering completed. Populated ${instagramApiConversations.length} conversations.`);
          } catch (error) {
            console.error(`❌ Error during conversation gathering:`, error);
            // Continue with processing even if gathering fails
          }
        } else {
          console.log(`✅ Conversation gathering already done. Found ${existingConversations} existing conversations.`);
        }

        // STEP 2: Process each new follower by matching with conversations
        console.log(`🔍 Processing each follower: Match with InstagramConversationsNotGathered table`);
        
        for (const follower of validatedData.followers) {
          const username = follower.instagramNickname;
          
          // Check if this follower has a conversation in our table
          const conversationData = await prisma.instagramConversationsNotGathered.findFirst({
            where: {
              organizationId,
              participantUsername: username
            }
          });
          
          console.log(`👤 Processing ${username}: ${conversationData ? 'HAS CONVERSATION → AI' : 'NO CONVERSATION → BATCH'}`);
          
          // Find the created follower record
          const followerRecord = await prisma.instagramFollower.findFirst({
            where: {
              organizationId,
              instagramNickname: username
            }
          });
          
          if (!followerRecord) {
            console.log(`⚠️ Follower record not found for ${username}, skipping`);
            continue;
          }

          if (conversationData && instagramSettings?.instagramToken) {
            // HAS CONVERSATION → AI ANALYSIS
            await processFollowerWithConversation(
              followerRecord,
              conversationData,
              instagramSettings.instagramToken,
              organizationId
            );
          } else {
            // NO CONVERSATION → MESSAGE BATCHES
            await processFollowerWithBatches(
              followerRecord,
              organizationId
            );
          }
        }
        
        console.log(`✅ Simple processing completed for ${results.newFollowers} followers`);
        
      } catch (error) {
        console.error('❌ Error in simple processing:', error);
        // Continue - don't fail the upload
      }
    }

    return NextResponse.json({
      success: true,
      message: `Successfully uploaded ${results.processed} followers. Added ${results.newFollowers} new followers to processing queue, ${results.existingFollowers} already existed.`,
      data: results
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error processing followers:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}

/**
 * Get processing status
 */
/**
 * Process follower with existing conversation - AI analysis
 */
async function processFollowerWithConversation(
  follower: any,
  conversationData: any,
  instagramToken: string,
  organizationId: string
) {
  try {
    console.log(`🤖 AI processing for ${follower.instagramNickname}...`);
    
    // Get conversation messages using conversationData.instagramConversationId
    const conversationResponse = await fetch(
      `https://graph.instagram.com/v22.0/${conversationData.instagramConversationId}/messages?access_token=${instagramToken}&fields=id,created_time,from,to,message,attachments{id,image_data,video_data,file_url,mime_type}`
    );
    
    if (!conversationResponse.ok) {
      console.log(`⚠️ Failed to get messages for ${follower.instagramNickname}, using batch instead`);
      return await processFollowerWithBatches(follower, organizationId);
    }
    
    const messageData = await conversationResponse.json();
    const messages = messageData.messages?.data || [];
    
    if (messages.length === 0) {
      console.log(`⚠️ No messages found for ${follower.instagramNickname}, using batch instead`);
      return await processFollowerWithBatches(follower, organizationId);
    }
    
    // Format conversation history for AI with timestamps (last 10 messages)
    const sortedMessages = messages.sort((a: any, b: any) =>
      new Date(a.created_time).getTime() - new Date(b.created_time).getTime()
    );
    
    // Take last 10 messages for AI analysis
    const last10Messages = sortedMessages.slice(-10);
    
    const conversationHistory = last10Messages.map((msg: any) => {
      const sender = msg.from?.username || msg.from?.id || 'Unknown';
      const messageText = msg.message || '[Media/Attachment]';
      const messageDate = new Date(msg.created_time).toISOString();
      return `[${messageDate}] ${sender}: ${messageText}`;
    }).join('\n');
    
    // AI analysis with conversation gathering mode
    const { generateInstagramResponse } = await import('@workspace/instagram-bot');
    const aiResponse = await generateInstagramResponse({
      prompt: "CONVERSATION GATHERING",
      conversationHistory: conversationHistory,
      organizationId: organizationId
    });
    
    console.log(`✅ AI analysis for ${follower.instagramNickname}: stage=${aiResponse.stage}, priority=${aiResponse.priority}, followUps=${aiResponse.followUps?.length || 0}`);
    
    // Create contact with AI-determined values
    const newContact = await prisma.instagramContact.create({
      data: {
        organizationId,
        userId: follower.userId,
        instagramId: follower.instagramId,
        instagramNickname: follower.instagramNickname,
        avatar: follower.avatar,
        followerCount: follower.followerCount,
        isVerifiedUser: follower.isVerified,
        stage: (aiResponse.stage as any) || 'new',
        priority: aiResponse.priority || 1,
        status: 'pending',
        messageCount: messages.length,
        nextMessageAt: new Date(),
        attackListStatus: 'pending',
        conversationSource: 'api',
        lastInteractionAt: new Date(conversationData.updatedTime)
      }
    });
    
    // Create AI-generated follow-ups
    if (aiResponse.followUps && aiResponse.followUps.length > 0) {
      for (let i = 0; i < aiResponse.followUps.length; i++) {
        const followUp = aiResponse.followUps[i];
        const scheduledTime = new Date();
        scheduledTime.setHours(scheduledTime.getHours() + (followUp.delayHours || 24));
        
        await prisma.instagramFollowUp.create({
          data: {
            contactId: newContact.id,
            sequenceNumber: i + 1,
            message: followUp.message,
            scheduledTime: scheduledTime,
            status: 'external'
          }
        });
      }
      console.log(`📝 Created ${aiResponse.followUps.length} follow-ups for ${follower.instagramNickname}`);
    }
    
    // Mark follower as contacted
    await prisma.instagramFollower.update({
      where: { id: follower.id },
      data: { status: 'contacted', isContacted: true }
    });
    
    // Mark conversation as gathered in InstagramConversationsNotGathered
    await prisma.instagramConversationsNotGathered.update({
      where: {
        instagramConversationId: conversationData.instagramConversationId
      },
      data: {
        isGathered: true
      }
    });
    
    console.log(`✅ ${follower.instagramNickname} processed with AI (priority ${aiResponse.priority || 1})`);
    
  } catch (error) {
    console.error(`❌ Error in AI processing for ${follower.instagramNickname}:`, error);
    // Fallback to batch processing
    await processFollowerWithBatches(follower, organizationId);
  }
}

/**
 * Process follower without conversation - message batches
 */
async function processFollowerWithBatches(follower: any, organizationId: string) {
  try {
    console.log(`📦 Batch processing for ${follower.instagramNickname}...`);
    
    // Get available message batches
    const messageBatches = await prisma.messageBatch.findMany({
      where: {
        organizationId,
        isActive: true
      },
      include: {
        MessageBatchItem: {
          orderBy: { sequenceNumber: 'asc' }
        }
      }
    });
    
    const batchesWithMessages = messageBatches.filter(
      batch => batch.MessageBatchItem.length > 0
    );
    
    const randomBatch = batchesWithMessages.length > 0
      ? batchesWithMessages[Math.floor(Math.random() * batchesWithMessages.length)]
      : null;
    
    // Create contact with priority 3 and batch messages
    const newContact = await prisma.instagramContact.create({
      data: {
        organizationId,
        userId: follower.userId,
        instagramId: follower.instagramId,
        instagramNickname: follower.instagramNickname,
        avatar: follower.avatar,
        followerCount: follower.followerCount,
        isVerifiedUser: follower.isVerified,
        stage: 'new',
        priority: 3, // Always priority 3 for batch processing
        status: 'pending',
        messageCount: 0,
        nextMessageAt: new Date(), // Ready immediately
        attackListStatus: 'pending',
        conversationSource: 'extension',
        batchId: randomBatch?.id
      }
    });
    
    // Mark follower as contacted
    await prisma.instagramFollower.update({
      where: { id: follower.id },
      data: { status: 'contacted', isContacted: true }
    });
    
    console.log(`✅ ${follower.instagramNickname} processed with batch messages (priority 3)`);
    
  } catch (error) {
    console.error(`❌ Error in batch processing for ${follower.instagramNickname}:`, error);
  }
}

export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check for API key first (Chrome extension)
    const apiKey = req.headers.get('X-API-Key');
    let organizationId: string;

    if (apiKey) {
      // API key authentication (Chrome extension)
      const result = await verifyApiKey(apiKey);
      if (!result.success) {
        return NextResponse.json(
          { success: false, error: result.errorMessage },
          { status: 401, headers: getCorsHeaders() }
        );
      }
      organizationId = result.organizationId;
    } else {
      // Session authentication (web dashboard)
      const session = await auth();
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401, headers: getCorsHeaders() }
        );
      }

      // Get user's organization
      const membership = await prisma.membership.findFirst({
        where: {
          userId: session.user.id
        }
      });

      if (!membership) {
        return NextResponse.json(
          { success: false, error: 'No organization found' },
          { status: 404, headers: getCorsHeaders() }
        );
      }

      organizationId = membership.organizationId;
    }

    // Get processing state
    const processingState = await prisma.instagramFollowerProcessingState.findUnique({
      where: {
        organizationId
      }
    });

    // Get counts
    const totalFollowers = await prisma.instagramFollower.count({
      where: { organizationId }
    });

    const pendingFollowers = await prisma.instagramFollower.count({
      where: {
        organizationId,
        status: 'pending'
      }
    });

    const contactedFollowers = await prisma.instagramFollower.count({
      where: {
        organizationId,
        status: 'contacted'
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        processingState: processingState || {
          position: 0,
          totalFollowers: null,
          isCompleted: false,
          lastProcessedAt: null
        },
        stats: {
          totalFollowers,
          pendingFollowers,
          contactedFollowers
        }
      }
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error getting processing status:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}
