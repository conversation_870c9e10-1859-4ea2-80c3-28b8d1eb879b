import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import crypto from 'crypto';

import { verifyApi<PERSON>ey } from '@workspace/api-keys';
import { prisma } from '@workspace/database/client';

// CORS headers for Chrome Extension
function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key, ngrok-skip-browser-warning',
    'Access-Control-Max-Age': '86400',
  };
}

// Handle preflight OPTIONS requests
export async function OPTIONS(req: NextRequest): Promise<Response> {
  return new NextResponse(null, {
    status: 200,
    headers: getCorsHeaders(),
  });
}

interface MessageToSend {
  id: string;
  username: string;
  message: string;
  type: 'batch' | 'batch_sequence' | 'followup';
  followUpId?: string;
  sequenceNumber?: number;
  batchMessages?: Array<{
    sequenceNumber: number;
    message: string;
    delayMinutes: number;
  }>;
  messageHash?: string; // SHA256 hash for duplicate detection
}

// Helper function to generate SHA256 hash
function generateMessageHash(message: string): string {
  return crypto.createHash('sha256').update(message).digest('hex');
}

// Helper function to check if daily counter needs reset
function needsDailyReset(lastResetDate: Date): boolean {
  const now = new Date();
  const lastReset = new Date(lastResetDate);
  
  // Reset if dates are different (new day)
  return now.toDateString() !== lastReset.toDateString();
}

/**
 * Get simplified messages ready to send for Chrome Extension
 * Returns only essential data: ID, Username, Message when time to send has arrived
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    const { searchParams } = new URL(req.url);
    const requestedOrganizationId = searchParams.get('organizationId');
    
    if (!requestedOrganizationId) {
      return NextResponse.json(
        { success: false, error: 'Organization ID is required' },
        { status: 400, headers: getCorsHeaders() }
      );
    }

    // Check for API key (Chrome extension)
    const apiKey = req.headers.get('X-API-Key');
    if (!apiKey) {
      return NextResponse.json(
        { success: false, error: 'API key required' },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    // Verify API key
    const result = await verifyApiKey(apiKey);
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.errorMessage },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    const organizationId = result.organizationId;
    
    // Validate that the requested organization ID matches the authenticated user's organization
    if (requestedOrganizationId !== organizationId) {
      return NextResponse.json(
        { success: false, error: 'Organization ID mismatch' },
        { status: 403, headers: getCorsHeaders() }
      );
    }

    const now = new Date();

    // Get Chrome Extension settings first to check limits
    let chromeExtensionSettings = await prisma.chromeExtensionSettings.findUnique({
      where: { organizationId }
    });

    // Check if daily counter needs reset
    if (chromeExtensionSettings && needsDailyReset(chromeExtensionSettings.lastMessageResetDate)) {
      chromeExtensionSettings = await prisma.chromeExtensionSettings.update({
        where: { organizationId },
        data: {
          messagesSentToday: 0,
          lastMessageResetDate: new Date()
        }
      });
    }

    // Check daily message limit
    const hasLimit = chromeExtensionSettings?.dailyMessageLimit !== null;
    const remainingMessages = hasLimit && chromeExtensionSettings?.dailyMessageLimit
      ? Math.max(0, chromeExtensionSettings.dailyMessageLimit - chromeExtensionSettings.messagesSentToday)
      : null;
    
    // If limit reached, return empty array with metadata
    if (hasLimit && remainingMessages === 0) {
      return NextResponse.json({
        success: true,
        data: [],
        count: 0,
        limitReached: true,
        metadata: {
          dailyLimit: chromeExtensionSettings?.dailyMessageLimit,
          messagesSentToday: chromeExtensionSettings?.messagesSentToday,
          remainingMessages: 0,
          resetTime: new Date(new Date().setHours(24, 0, 0, 0)).toISOString(),
          timestamp: now.toISOString()
        }
      }, {
        headers: getCorsHeaders()
      });
    }

    // Get contacts ready for messaging (time has arrived AND currently in attack list)
    const readyContacts = await prisma.instagramContact.findMany({
      where: {
        organizationId,
        attackListStatus: 'pending', // Must be in attack list
        conversationSource: 'extension',
        stage: {
          notIn: ['converted', 'disqualified', 'blocked', 'suspicious'] // Exclude terminal stages (same as attack-list endpoint)
        }
      },
      include: {
        InstagramFollowUp: {
          where: {
            status: 'external'
          },
          orderBy: { sequenceNumber: 'asc' }
        }
      },
      orderBy: [
        { priority: 'desc' },
        { nextMessageAt: 'asc' }
      ]
    });

    // Process contacts and handle overdue follow-up rescheduling
    const contactsWithReadyMessages = [];
    
    for (const contact of readyContacts) {
      const followUps = contact.InstagramFollowUp;
      
      if (followUps.length > 0) {
        // Check for overdue follow-ups that need rescheduling
        const overdueFollowUps = followUps.filter(f => f.scheduledTime <= now);
        const futureFollowUps = followUps.filter(f => f.scheduledTime > now);
        
        console.log(`📊 Contact ${contact.instagramNickname}: ${followUps.length} total follow-ups, ${overdueFollowUps.length} overdue, ${futureFollowUps.length} future`);
        
        if (overdueFollowUps.length > 1) {
          console.log(`🔄 Contact ${contact.instagramNickname} has ${overdueFollowUps.length} overdue follow-ups - rescheduling remaining to maintain intervals`);
          console.log(`📅 Original schedule:`, followUps.map(f => ({ seq: f.sequenceNumber, time: f.scheduledTime.toISOString() })));
          
          // Sort overdue follow-ups by sequence number to ensure proper order
          const sortedOverdueFollowUps = overdueFollowUps.sort((a, b) => a.sequenceNumber - b.sequenceNumber);
          
          // Keep the first (earliest) overdue follow-up for immediate sending
          const firstOverdueFollowUp = sortedOverdueFollowUps[0];
          console.log(`📤 Keeping first overdue follow-up #${firstOverdueFollowUp.sequenceNumber} for immediate sending`);
          
          // Reschedule only the remaining overdue follow-ups (skip the first one)
          const remainingOverdueFollowUps = sortedOverdueFollowUps.slice(1);
          
          if (remainingOverdueFollowUps.length > 0) {
            // Calculate intervals between the remaining follow-ups
            const intervals: number[] = [];
            const minIntervalMs = 24 * 60 * 60 * 1000; // Minimum 24 hours between follow-ups
            
            for (let i = 1; i < remainingOverdueFollowUps.length; i++) {
              const prevFollowUp = remainingOverdueFollowUps[i - 1];
              const currentFollowUp = remainingOverdueFollowUps[i];
              const originalInterval = currentFollowUp.scheduledTime.getTime() - prevFollowUp.scheduledTime.getTime();
              
              // Use original interval if reasonable, otherwise use minimum 24h
              const interval = originalInterval > minIntervalMs ? originalInterval : minIntervalMs;
              intervals.push(interval);
            }
            
            console.log(`📊 Calculated intervals for remaining follow-ups:`, intervals.map(i => `${Math.round(i / (1000 * 60 * 60))}h`));
            
            // Reschedule remaining follow-ups starting from 24h after now
            const reschedulePromises = [];
            let nextScheduleTime = now.getTime() + (24 * 60 * 60 * 1000); // Start 24h from now
            
            for (let i = 0; i < remainingOverdueFollowUps.length; i++) {
              const followUp = remainingOverdueFollowUps[i];
              const newScheduledTime = new Date(nextScheduleTime);
              
              reschedulePromises.push(
                prisma.instagramFollowUp.update({
                  where: { id: followUp.id },
                  data: { scheduledTime: newScheduledTime }
                })
              );
              console.log(`📅 Rescheduling ${contact.instagramNickname} follow-up #${followUp.sequenceNumber} from ${followUp.scheduledTime.toISOString()} to ${newScheduledTime.toISOString()}`);
              
              // Calculate next schedule time using calculated intervals
              if (i < intervals.length) {
                nextScheduleTime += intervals[i];
              } else {
                nextScheduleTime += 24 * 60 * 60 * 1000; // Default 24h interval for any extra follow-ups
              }
            }
            
            // Execute all reschedule operations
            if (reschedulePromises.length > 0) {
              await Promise.all(reschedulePromises);
              
              // Refresh follow-up data after rescheduling
              const updatedContact = await prisma.instagramContact.findUnique({
                where: { id: contact.id },
                include: {
                  InstagramFollowUp: {
                    where: { status: 'external' },
                    orderBy: { sequenceNumber: 'asc' }
                  }
                }
              });
              
              if (updatedContact) {
                // Update contact's follow-ups with fresh data
                contact.InstagramFollowUp = updatedContact.InstagramFollowUp;
              }
            }
          }
        }
        
        // Check if this contact has any follow-up ready now (using current/updated data)
        const nextReadyFollowUp = contact.InstagramFollowUp.find(f => f.scheduledTime <= now);
        
        if (nextReadyFollowUp) {
          contactsWithReadyMessages.push(contact);
        }
      } else if (contact.priority === 3 && contact.stage === 'new' && (!contact.nextMessageAt || contact.nextMessageAt <= now)) {
        // New follower ready for initial batch message
        contactsWithReadyMessages.push(contact);
      }
    }

    const smartFocus = chromeExtensionSettings?.smartFocus ?? false;

    // Apply smart focus sorting if enabled (priority 3 first)
    let sortedContacts = contactsWithReadyMessages;
    if (smartFocus) {
      sortedContacts = [
        ...contactsWithReadyMessages.filter(c => c.priority === 3),
        ...contactsWithReadyMessages.filter(c => c.priority !== 3)
      ];
    }

    // Get message batches for new followers without conversations
    const messageBatches = await prisma.messageBatch.findMany({
      where: { organizationId },
      include: {
        MessageBatchItem: {
          orderBy: { sequenceNumber: 'asc' }
        }
      }
    });

    const messagesToSend: MessageToSend[] = [];

    for (const contact of sortedContacts) {
      // Check if we've reached the daily limit (for limited plans)
      if (hasLimit && remainingMessages !== null && messagesToSend.length >= remainingMessages) {
        console.log(`📊 Daily message limit reached: ${messagesToSend.length}/${remainingMessages}`);
        break;
      }

      // Double-check: exclude disqualified contacts (defensive programming)
      if (contact.stage === 'disqualified') {
        console.log(`⚠️ Skipping disqualified contact: ${contact.instagramNickname}`);
        continue;
      }
      
      // Find the next follow-up that should be sent (earliest overdue only)
      const nextFollowUp = contact.InstagramFollowUp.find(f => f.scheduledTime <= now);
      
      if (nextFollowUp) {
        // Contact has AI follow-up ready to send
        const messageHash = generateMessageHash(nextFollowUp.message);
        
        messagesToSend.push({
          id: contact.id,
          username: contact.instagramNickname,
          message: nextFollowUp.message,
          type: 'followup',
          followUpId: nextFollowUp.id,
          sequenceNumber: nextFollowUp.sequenceNumber,
          messageHash
        });
      } else if (contact.priority === 3 && contact.stage === 'new') {
        // Contact is new follower without conversations - return ENTIRE batch sequence
        const batchesWithMessages = messageBatches.filter(
          batch => batch.MessageBatchItem.length > 0
        );

        if (batchesWithMessages.length > 0) {
          const randomIndex = Math.floor(Math.random() * batchesWithMessages.length);
          const selectedBatch = batchesWithMessages[randomIndex];
          
          // Generate hash for the first message (for duplicate detection)
          const firstMessage = selectedBatch.MessageBatchItem[0]?.messageText || '';
          const messageHash = generateMessageHash(firstMessage);
          
          // Check if this first message was already sent to this contact
          if (contact.firstMessageHash === messageHash) {
            console.log(`⚠️ Duplicate first message detected for ${contact.instagramNickname}, skipping`);
            continue;
          }

          // Return ALL messages in the batch - Chrome extension will handle delays
          messagesToSend.push({
            id: contact.id,
            username: contact.instagramNickname,
            message: selectedBatch.MessageBatchItem.map(item => item.messageText).join(' | '), // All messages separated
            type: 'batch_sequence',
            sequenceNumber: 1,
            batchMessages: selectedBatch.MessageBatchItem.map(item => ({
              sequenceNumber: item.sequenceNumber,
              message: item.messageText,
              delayMinutes: 0 // Chrome extension uses hardcoded 20-40 second delays between messages
            })),
            messageHash
          });
        }
      }
    }

    return NextResponse.json({
      success: true,
      data: messagesToSend,
      count: messagesToSend.length,
      limitReached: false,
      metadata: {
        totalReadyContacts: contactsWithReadyMessages.length,
        totalInAttackList: readyContacts.length,
        smartFocusEnabled: smartFocus,
        dailyLimit: chromeExtensionSettings?.dailyMessageLimit,
        messagesSentToday: chromeExtensionSettings?.messagesSentToday || 0,
        remainingMessages: hasLimit ? remainingMessages : null,
        resetTime: hasLimit ? new Date(new Date().setHours(24, 0, 0, 0)).toISOString() : null,
        timestamp: now.toISOString()
      }
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error fetching messages to send:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}
