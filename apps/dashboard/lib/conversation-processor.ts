import { prisma } from '@workspace/database/client';
import { generateInstagramResponse } from '@workspace/instagram-bot';
import { getConversationMessages } from '~/lib/instagram-client';

export interface ConversationProcessingResult {
  success: boolean;
  processed: number;
  failed: number;
  errors: string[];
}

export class ConversationProcessor {
  private isProcessing = false;
  private readonly PROCESSING_TIMEOUT = 300000; // 5 minutes
  private readonly MAX_RETRIES = 3;
  private readonly RATE_LIMIT_DELAY = 2000; // 2 seconds between AI calls

  /**
   * Process all unprocessed conversations for an organization
   */
  async processOrganizationConversations(organizationId: string): Promise<ConversationProcessingResult> {
    if (this.isProcessing) {
      return { success: false, processed: 0, failed: 0, errors: ['Already processing'] };
    }

    this.isProcessing = true;
    console.log(`🚀 Starting conversation processing for organization: ${organizationId}`);

    try {
      // Get Instagram settings
      const instagramSettings = await prisma.instagramSettings.findFirst({
        where: {
          organizationId,
          instagramToken: { not: null }
        }
      });

      if (!instagramSettings?.instagramToken) {
        throw new Error('No Instagram token found');
      }

      // Get all unprocessed conversations
      const unprocessedConversations = await prisma.instagramConversationsNotGathered.findMany({
        where: {
          organizationId,
          isGathered: false
        },
        orderBy: { updatedTime: 'desc' }
      });

      console.log(`📋 Found ${unprocessedConversations.length} unprocessed conversations`);

      const result: ConversationProcessingResult = {
        success: true,
        processed: 0,
        failed: 0,
        errors: []
      };

      for (const conversation of unprocessedConversations) {
        try {
          const processResult = await this.processSingleConversation(
            conversation,
            instagramSettings.instagramToken,
            organizationId
          );

          if (processResult.success) {
            result.processed++;
          } else {
            result.failed++;
            result.errors.push(`${conversation.participantUsername}: ${processResult.error}`);
          }

          // Rate limiting delay
          if (unprocessedConversations.indexOf(conversation) < unprocessedConversations.length - 1) {
            await new Promise(resolve => setTimeout(resolve, this.RATE_LIMIT_DELAY));
          }

        } catch (error) {
          result.failed++;
          const errorMsg = error instanceof Error ? error.message : 'Unknown error';
          result.errors.push(`${conversation.participantUsername}: ${errorMsg}`);
          console.error(`❌ Error processing conversation for ${conversation.participantUsername}:`, error);
        }
      }

      console.log(`✅ Conversation processing completed. Processed: ${result.processed}, Failed: ${result.failed}`);
      return result;

    } catch (error) {
      console.error('❌ Error in conversation processing:', error);
      return {
        success: false,
        processed: 0,
        failed: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      };
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Process a single conversation with comprehensive error handling
   */
  private async processSingleConversation(
    conversation: any,
    instagramToken: string,
    organizationId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`🔄 Processing conversation for ${conversation.participantUsername}...`);

      // Get conversation messages with timeout
      const conversationResponse = await Promise.race([
        getConversationMessages(conversation.instagramConversationId, instagramToken),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Instagram API timeout')), this.PROCESSING_TIMEOUT)
        )
      ]) as any;

      if (!conversationResponse?.data?.[0]?.messages?.data) {
        console.log(`⚠️ No messages found for ${conversation.participantUsername}`);
        await this.markConversationAsGathered(conversation.id);
        return { success: true };
      }

      const messages = conversationResponse.data[0].messages.data;
      console.log(`📝 Found ${messages.length} messages for ${conversation.participantUsername}`);

      // Format conversation history
      const conversationHistory = await this.formatConversationHistory(
        messages,
        conversation.participantUsername
      );

      // AI analysis with timeout and retry
      const aiResponse = await this.analyzeConversationWithRetry(
        conversationHistory,
        organizationId,
        conversation.participantUsername
      );

      // Create or update contact
      await this.createOrUpdateContact(
        conversation,
        aiResponse,
        messages,
        organizationId
      );

      // Mark as processed
      await this.markConversationAsGathered(conversation.id);

      console.log(`✅ Successfully processed conversation for ${conversation.participantUsername}`);
      return { success: true };

    } catch (error) {
      console.error(`❌ Error processing conversation for ${conversation.participantUsername}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Format conversation history for AI analysis
   */
  private formatConversationHistory(messages: any[], participantUsername: string): string {
    const sortedMessages = messages.sort((a: any, b: any) =>
      new Date(a.created_time).getTime() - new Date(b.created_time).getTime()
    );

    // Filter out extension messages
    const filteredMessages = sortedMessages.filter((msg: any) => !msg.isFromExtension);

    let conversationHistory = filteredMessages.map((msg: any) => {
      const sender = msg.from?.username || msg.from?.id || 'Unknown';
      const messageText = msg.message || '[Media/Attachment]';
      return `${sender}: ${messageText}`;
    }).join('\n');

    // Add last user interaction timestamp
    const lastUserMessage = sortedMessages
      .filter((msg: any) => msg.from?.username === participantUsername)
      .pop();

    if (lastUserMessage) {
      const lastInteractionTime = new Date(lastUserMessage.created_time);
      conversationHistory += `\n\nLAST USER INTERACTION: ${lastInteractionTime.toISOString()}`;
    }

    return conversationHistory;
  }

  /**
   * Analyze conversation with AI including retry logic
   */
  private async analyzeConversationWithRetry(
    conversationHistory: string,
    organizationId: string,
    participantUsername: string,
    attempt: number = 1
  ): Promise<any> {
    try {
      console.log(`🤖 AI analysis attempt ${attempt} for ${participantUsername}...`);

      const aiResponse = await Promise.race([
        generateInstagramResponse({
          prompt: "CONVERSATION GATHERING",
          conversationHistory: conversationHistory,
          organizationId: organizationId
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('AI analysis timeout')), this.PROCESSING_TIMEOUT)
        )
      ]);

      console.log(`✅ AI analysis successful for ${participantUsername}:`, {
        stage: (aiResponse as any).stage,
        priority: (aiResponse as any).priority,
        followUpsCount: (aiResponse as any).followUps?.length || 0
      });

      return aiResponse;

    } catch (error) {
      console.error(`❌ AI analysis attempt ${attempt} failed for ${participantUsername}:`, error);

      if (attempt < this.MAX_RETRIES) {
        console.log(`🔄 Retrying AI analysis for ${participantUsername} (attempt ${attempt + 1}/${this.MAX_RETRIES})...`);
        // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, this.RATE_LIMIT_DELAY * attempt));
        return this.analyzeConversationWithRetry(conversationHistory, organizationId, participantUsername, attempt + 1);
      }

      // Return default values after max retries
      console.log(`⚠️ Using default values for ${participantUsername} after ${this.MAX_RETRIES} failed attempts`);
      return {
        stage: 'new',
        priority: 3,
        followUps: []
      };
    }
  }

  /**
   * Create or update contact based on conversation analysis
   */
  private async createOrUpdateContact(
    conversation: any,
    aiResponse: any,
    messages: any[],
    organizationId: string
  ): Promise<void> {
    // Check if contact already exists
    const existingContact = await prisma.instagramContact.findFirst({
      where: {
        organizationId,
        instagramNickname: conversation.participantUsername
      }
    });

    if (existingContact) {
      console.log(`📝 Updating existing contact: ${conversation.participantUsername}`);
      await this.updateExistingContact(existingContact, aiResponse, messages);
    } else {
      console.log(`📝 Creating new contact: ${conversation.participantUsername}`);
      await this.createNewContact(conversation, aiResponse, messages, organizationId);
    }
  }

  /**
   * Update existing contact with new conversation data
   */
  private async updateExistingContact(existingContact: any, aiResponse: any, messages: any[]): Promise<void> {
    // Clear existing follow-ups
    await prisma.instagramFollowUp.deleteMany({
      where: { contactId: existingContact.id }
    });

    // Create new follow-ups
    let earliestFollowUpTime = null;
    if (aiResponse.followUps && aiResponse.followUps.length > 0) {
      for (let i = 0; i < aiResponse.followUps.length; i++) {
        const followUp = aiResponse.followUps[i];
        const scheduledTime = new Date();
        scheduledTime.setHours(scheduledTime.getHours() + (followUp.delayHours || 24));

        if (!earliestFollowUpTime || scheduledTime < earliestFollowUpTime) {
          earliestFollowUpTime = scheduledTime;
        }

        await prisma.instagramFollowUp.create({
          data: {
            contactId: existingContact.id,
            sequenceNumber: i + 1,
            message: followUp.message,
            scheduledTime: scheduledTime,
            status: 'external'
          }
        });
      }
    }

    // Update contact
    await prisma.instagramContact.update({
      where: { id: existingContact.id },
      data: {
        stage: (aiResponse.stage as any) || existingContact.stage,
        priority: aiResponse.priority || existingContact.priority,
        nextMessageAt: earliestFollowUpTime || new Date(),
        attackListStatus: 'pending',
        conversationSource: 'api', // Update to 'api' since this is from conversation gathering
        updatedAt: new Date()
      }
    });

    // Save messages
    await this.saveMessages(messages, existingContact.id, existingContact.instagramNickname);
  }

  /**
   * Create new contact from conversation analysis
   */
  private async createNewContact(
    conversation: any,
    aiResponse: any,
    messages: any[],
    organizationId: string
  ): Promise<void> {
    // Get organization owner for userId
    const membership = await prisma.membership.findFirst({
      where: {
        organizationId,
        isOwner: true
      }
    });

    if (!membership) {
      throw new Error('No organization owner found');
    }

    const newContact = await prisma.instagramContact.create({
      data: {
        organizationId,
        userId: membership.userId,
        instagramNickname: conversation.participantUsername,
        instagramId: conversation.participantId,
        stage: (aiResponse.stage as any) || 'new',
        priority: aiResponse.priority || 3,
        conversationSource: 'api',
        nextMessageAt: new Date(),
        attackListStatus: 'pending',
        lastInteractionAt: new Date(conversation.updatedTime)
      }
    });

    // Save messages
    await this.saveMessages(messages, newContact.id, conversation.participantUsername);

    // Create follow-ups
    if (aiResponse.followUps && aiResponse.followUps.length > 0) {
      for (let i = 0; i < aiResponse.followUps.length; i++) {
        const followUp = aiResponse.followUps[i];
        const scheduledTime = new Date();
        scheduledTime.setHours(scheduledTime.getHours() + (followUp.delayHours || 24));

        await prisma.instagramFollowUp.create({
          data: {
            contactId: newContact.id,
            sequenceNumber: i + 1,
            message: followUp.message,
            scheduledTime: scheduledTime,
            status: 'external'
          }
        });
      }
    }
  }

  /**
   * Save conversation messages to database
   */
  private async saveMessages(messages: any[], contactId: string, participantUsername: string): Promise<void> {
    for (const msg of messages) {
      try {
        const existingMessage = await prisma.instagramMessage.findFirst({
          where: {
            contactId,
            messageId: msg.id
          }
        });

        if (!existingMessage) {
          await prisma.instagramMessage.create({
            data: {
              contactId,
              messageId: msg.id,
              content: msg.message || '[Media/Attachment]',
              isFromUser: msg.from?.username === participantUsername,
              isFromExtension: msg.isFromExtension || false,
              timestamp: new Date(msg.created_time),
              mediaType: msg.attachments?.[0]?.mime_type || null,
              mediaUrl: msg.attachments?.[0]?.file_url || null
            }
          });
        }
      } catch (error) {
        console.error(`Error saving message ${msg.id}:`, error);
      }
    }
  }

  /**
   * Mark conversation as gathered
   */
  private async markConversationAsGathered(conversationId: string): Promise<void> {
    await prisma.instagramConversationsNotGathered.update({
      where: { id: conversationId },
      data: { isGathered: true }
    });
  }

  /**
   * Get processing statistics
   */
  async getProcessingStats(organizationId: string) {
    const [total, gathered, pending] = await Promise.all([
      prisma.instagramConversationsNotGathered.count({
        where: { organizationId }
      }),
      prisma.instagramConversationsNotGathered.count({
        where: { organizationId, isGathered: true }
      }),
      prisma.followerProcessingQueue.count({
        where: { organizationId, status: 'pending', hasConversation: true }
      })
    ]);

    return {
      totalConversations: total,
      gatheredConversations: gathered,
      pendingConversations: total - gathered,
      pendingQueueItems: pending,
      percentComplete: total > 0 ? Math.round((gathered / total) * 100) : 0,
      isComplete: total > 0 && gathered === total && pending === 0
    };
  }

  /**
   * Clean up stuck processing states
   */
  async cleanupStuckProcessing(organizationId: string): Promise<number> {
    const stuckThreshold = new Date(Date.now() - this.PROCESSING_TIMEOUT);

    // Reset stuck queue items
    const result = await prisma.followerProcessingQueue.updateMany({
      where: {
        organizationId,
        status: 'processing',
        updatedAt: { lt: stuckThreshold }
      },
      data: {
        status: 'pending',
        updatedAt: new Date()
      }
    });

    console.log(`🧹 Reset ${result.count} stuck processing items`);
    return result.count;
  }
}

// Export singleton instance
export const conversationProcessor = new ConversationProcessor();