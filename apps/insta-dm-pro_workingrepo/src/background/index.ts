import Browser from 'webextension-polyfill'
import { GeneralConfig } from '@shared/config'
import DataLayer from '@shared/datalayer'
import { MessageTypes } from '@shared/messaging'
import delay from './Utils/delay'
import { ApiService } from '@shared/api-service'
import { FollowUpManager } from './follow-up-manager'

const heartbeatAlarmName = 'heartbeat'
const followUpAlarmName = 'followUpCheck'
// newFollowersCheckAlarmName removed - monitoring now integrated during wait times
let heartbeatScheduledAlreadyRunning = false
let scheduledScanIsAlive = false
let running = false;
let followerCheckIsRunning = false;
let monitor_start_timestamp = 0; // Track when monitoring started
let conversationGatheringPollingActive = false; // Track if we're polling for conversation gathering status

// Create heartbeat alarm if it doesn't exist
async function createHeartbeatAlarm() {
  const alarms = await Browser.alarms.getAll()
  const alarmExists = alarms.some((alarm) => alarm.name === heartbeatAlarmName)

  if (!alarmExists) {
    Browser.alarms.create(heartbeatAlarmName, {
      periodInMinutes: 0.5, // Run every 30 seconds for better responsiveness
    })
  }
}

// Create follow-up check alarm if it doesn't exist
async function createFollowUpAlarm() {
  const alarms = await Browser.alarms.getAll()
  const alarmExists = alarms.some((alarm) => alarm.name === followUpAlarmName)

  if (!alarmExists) {
    Browser.alarms.create(followUpAlarmName, {
      periodInMinutes: 1, // Check every 1 minute
    })
  }
}

// Note: createNewFollowersCheckAlarm removed - monitoring now integrated during wait times

async function setStatus(text: string) {
  let config: any = await DataLayer.getConfig()
  const updatedConfig = {
    ...config,
    status: text,
  }
  await DataLayer.setConfig(updatedConfig)
}

createHeartbeatAlarm()
createFollowUpAlarm()
// Note: newFollowersCheck alarm removed - now monitoring during wait times instead

const getMainTab = async () => {
  const mainTabId = await DataLayer.getMainTabId()
  let tab = null
  if (mainTabId) {
    try {
      tab = await Browser.tabs.get(parseInt(mainTabId))
    } catch (error) {
      // Tab was closed, create a new one
      tab = null
    }
  }

  if (!tab) {
    tab = await Browser.tabs.create({
      url: 'https://www.instagram.com/direct/inbox/',
      active: false,
    })
    await DataLayer.setMainTabId(tab.id!.toString())
  }
  
  await Browser.tabs.update(tab.id!, { autoDiscardable: false });
  await delay(2000);
  return tab;
}

async function waitForTabToLoad(tab: Browser.Tabs.Tab) {
  if (!tab || !tab.id) {
    throw new Error('Invalid tab object.')
  }

  const tabInfo = await Browser.tabs.get(tab.id)
  if (tabInfo.status === 'complete') {
    return tab
  }

  await new Promise((resolve) => {
    const listener = (tabId: any, changeInfo: any) => {
      if (tabId === tab.id && changeInfo.status === 'complete') {
        Browser.tabs.onUpdated.removeListener(listener)
        resolve(true)
      }
    }
    Browser.tabs.onUpdated.addListener(listener)
  })

  return tab
}

// Get random delay between min and max values
const getRandomDelay = (min: number, max: number): number => {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

// Check if current time is within natural pause period
const isWithinNaturalPause = (pauseStart: string, pauseStop: string): boolean => {
  const now = new Date()
  const currentTime = now.getHours() * 60 + now.getMinutes()
  
  const [startHour, startMin] = pauseStart.split(':').map(Number)
  const [stopHour, stopMin] = pauseStop.split(':').map(Number)
  
  const startTime = startHour * 60 + startMin
  const stopTime = stopHour * 60 + stopMin
  
  if (startTime <= stopTime) {
    return currentTime >= startTime && currentTime <= stopTime
  } else {
    // Overnight pause (e.g., 23:00 to 07:00)
    return currentTime >= startTime || currentTime <= stopTime
  }
}

// Check if there are messages to send
const checkIfHasMessages = async (apiKey: string): Promise<boolean> => {
  try {
    const messagesToSendResult = await ApiService.getMessagesToSend(apiKey)
    return !!(messagesToSendResult.success &&
              messagesToSendResult.data &&
              messagesToSendResult.data.length > 0)
  } catch (error) {
    console.error('Error checking for messages:', error)
    return false
  }
}

// Check if scraping is allowed and perform if needed
const checkAndPerformScraping = async (apiKey: string, settings: any) => {
  // Apply default values for scraping settings if not provided by API
  const scrapingSettings = {
    scrapingEnabled: settings.scrapingEnabled !== false, // Default to true unless explicitly disabled
    scrapingIntervalMin: settings.scrapingIntervalMin || 15, // Default 15 minutes
    scrapingIntervalMax: settings.scrapingIntervalMax || 30, // Default 30 minutes
    nextScrapingAllowedAt: settings.nextScrapingAllowedAt
  }

  console.log('🔍 SCRAPING: DETAILED DEBUG - Starting scraping check...')
  
  // First check if we have messages (for continuous scraping logic)
  const hasMessages = await checkIfHasMessages(apiKey)
  
  // Check scraping eligibility from API to get authoritative status
  console.log('🔍 SCRAPING: Checking eligibility from API...')
  const eligibilityResult = await ApiService.checkScrapingEligibility(
    apiKey,
    !hasMessages, // continuous mode if no messages
    hasMessages   // whether we have messages to send
  )
  
  if (eligibilityResult.success && eligibilityResult.data) {
    const eligibility = eligibilityResult.data
    console.log('🔍 SCRAPING: API Eligibility Check:', {
      isEligible: eligibility.isEligible,
      extensionStatus: eligibility.extensionStatus,
      totalFollowersScraped: eligibility.totalFollowersScraped,
      nextAllowedAt: eligibility.nextAllowedAt,
      waitTimeHuman: eligibility.waitTimeHuman,
      allFollowersScraped: eligibility.allFollowersScraped,
      scrapingType: eligibility.scrapingType,
      reason: eligibility.reason
    })
    
    // 🚨 CRITICAL: Only allow initial scraping if status is FRESH_START
    if (eligibility.extensionStatus !== 'FRESH_START') {
      console.log(`🔍 SCRAPING: ❌ Extension status is ${eligibility.extensionStatus}, not FRESH_START - regular scraping not allowed`)
      
      // For non-FRESH_START status, only allow periodic progressive scraping
      if (eligibility.extensionStatus.startsWith('SCRAPED_') || eligibility.extensionStatus === 'ACTIVE') {
        console.log(`🔍 SCRAPING: ✅ Status allows progressive scraping: ${eligibility.extensionStatus}`)
        // Continue with progressive scraping logic for ongoing monitoring
      } else {
        await setStatus(`✅ Initial scraping already completed`)
        return
      }
    } else {
      console.log(`🔍 SCRAPING: ✅ FRESH_START status - initial scraping allowed`)
    }
    
    // If API says not eligible (even in FRESH_START), respect that decision
    if (!eligibility.isEligible) {
      console.log(`🔍 SCRAPING: ❌ API says not eligible - wait ${eligibility.waitTimeHuman}`)
      await setStatus(`⏳ Next scraping in ${eligibility.waitTimeHuman}`)
      return
    }
  }
  
  console.log('🔍 SCRAPING: API Settings:', {
    scrapingEnabled: scrapingSettings.scrapingEnabled,
    scrapingInterval: `${scrapingSettings.scrapingIntervalMin}-${scrapingSettings.scrapingIntervalMax} minutes`,
    nextScrapingAllowedAt: scrapingSettings.nextScrapingAllowedAt,
    apiProvidedSettings: !!settings.scrapingEnabled,
    pauseStart: settings.pauseStart,
    pauseStop: settings.pauseStop
  })

  // Check if scraping is enabled in settings
  if (!scrapingSettings.scrapingEnabled) {
    console.log('🔍 SCRAPING: ❌ CONDITION FAILED - Disabled in settings, skipping')
    await setStatus('❌ Scraping disabled in settings')
    return
  }
  console.log('🔍 SCRAPING: ✅ CONDITION PASSED - Scraping is enabled')

  // Check if we're in natural pause period (respect same pause as messaging)
  const currentTime = new Date()
  const isInPause = isWithinNaturalPause(settings.pauseStart, settings.pauseStop)
  if (isInPause) {
    console.log('🔍 SCRAPING: ❌ CONDITION FAILED - In natural pause period, skipping')
    console.log(`🔍 SCRAPING: Current time: ${currentTime.toLocaleTimeString()}, Pause: ${settings.pauseStart} - ${settings.pauseStop}`)
    await setStatus(`😴 Natural pause active - No scraping (${settings.pauseStart} - ${settings.pauseStop})`)
    return
  }
  console.log('🔍 SCRAPING: ✅ CONDITION PASSED - Not in natural pause period')
  console.log(`🔍 SCRAPING: Current time: ${currentTime.toLocaleTimeString()}, Pause: ${settings.pauseStart} - ${settings.pauseStop}`)

  // 🚨 FIX: Make API settings authoritative - only use local storage if API doesn't have nextScrapingAllowedAt
  let nextAllowedTime: Date | null = null
  let timeSource = 'none'
  
  // First check API settings (these are authoritative)
  if (scrapingSettings.nextScrapingAllowedAt) {
    nextAllowedTime = new Date(scrapingSettings.nextScrapingAllowedAt)
    timeSource = 'API'
    console.log('🔍 SCRAPING: Using API nextScrapingAllowedAt:', nextAllowedTime.toISOString())
  } else {
    // Only check local storage if API doesn't have a setting
    try {
      const localStorageResult = await Browser.storage.local.get(['nextScrapingAllowedAt'])
      if (localStorageResult.nextScrapingAllowedAt) {
        const localTime = new Date(localStorageResult.nextScrapingAllowedAt)
        nextAllowedTime = localTime
        timeSource = 'local storage'
        console.log('🔍 SCRAPING: Using local storage nextScrapingAllowedAt:', nextAllowedTime.toISOString())
      }
    } catch (error) {
      console.log('🔍 SCRAPING: Error reading local storage for next scraping time:', error)
    }
  }
  
  // Enhanced debug logging for time checks
  const now = new Date()
  console.log('🔍 SCRAPING: Time check details:', {
    currentTime: now.toISOString(),
    nextAllowedTime: nextAllowedTime?.toISOString() || 'null',
    timeSource,
    canScrapeNow: !nextAllowedTime || now >= nextAllowedTime
  })
  
  if (nextAllowedTime && now < nextAllowedTime) {
    const timeLeft = Math.ceil((nextAllowedTime.getTime() - now.getTime()) / (1000 * 60)) // minutes
    console.log(`🔍 SCRAPING: ❌ CONDITION FAILED - Too early, next allowed in ${timeLeft} minutes`)
    console.log(`🔍 SCRAPING: Wait until: ${nextAllowedTime.toISOString()} (source: ${timeSource})`)
    await setStatus(`⏳ Next scraping in ${timeLeft} minutes (${nextAllowedTime.toLocaleTimeString()})`)
    return
  }
  console.log('🔍 SCRAPING: ✅ CONDITION PASSED - Time check passed, scraping allowed')

  // All conditions passed - trigger appropriate scraping type
  console.log('🔍 SCRAPING: 🚀 ALL CONDITIONS PASSED! Starting follower collection...')
  
  try {
    // Check if this is initial scraping (FRESH_START) or progressive scraping
    if (eligibilityResult.success && eligibilityResult.data?.extensionStatus === 'FRESH_START') {
      console.log('🔍 SCRAPING: Triggering INITIAL scraping (250 most recent followers)')
      await setStatus('🚀 Starting initial scraping session...')
      await performInitialScraping()
    } else {
      console.log('🔍 SCRAPING: Triggering PROGRESSIVE scraping (continuing from previous position)')
      await setStatus('🔍 Starting progressive scraping session...')
      // After scraping, update the timestamp based on type
      const isContinuous = eligibilityResult.data?.scrapingType === 'continuous'
      await performFollowerScraping(apiKey, scrapingSettings, isContinuous)
    }
  } catch (error) {
    console.error('🔍 SCRAPING: Error during scraping:', error)
    await setStatus('❌ Scraping error - will retry later')
  }
}

// Perform the actual follower scraping
const performFollowerScraping = async (apiKey: string, settings: any, isContinuous: boolean = false) => {
  console.log(`🔍 SCRAPING: Starting ${isContinuous ? 'CONTINUOUS' : 'progressive'} follower panel scraping session...`)
  
  // Get current position before scraping
  const scrapingProgressResult = await Browser.storage.local.get(['scrapingPosition', 'totalFollowersScraped'])
  const currentPosition = scrapingProgressResult.scrapingPosition || 0
  const totalScraped = scrapingProgressResult.totalFollowersScraped || 0
  
  await setStatus(`🔍 Progressive scraping from position ${currentPosition} (${totalScraped} total scraped)...`)
  
  // 🔥 CRITICAL FIX: Ensure tab is active during scraping to prevent throttling
  const tab = await getMainTab()
  if (tab && tab.id) {
    try {
      // Make tab active (focused) to prevent browser throttling
      await Browser.tabs.update(tab.id, { active: true })
      if (tab.windowId) {
        await Browser.windows.update(tab.windowId, { focused: true })
      }
      console.log('🔍 SCRAPING: ✅ Tab made active to prevent throttling')
      
      // Wait for tab to become fully active
      await delay(1000)
    } catch (error) {
      console.log('🔍 SCRAPING: ⚠️ Could not activate tab, continuing anyway:', error)
    }
  }
  
  // Use existing scraping logic but with progressive positioning
  await checkForNewFollowers()
  
  // Get updated position after scraping
  const updatedProgressResult = await Browser.storage.local.get(['scrapingPosition', 'totalFollowersScraped'])
  const newPosition = updatedProgressResult.scrapingPosition || 0
  const newTotalScraped = updatedProgressResult.totalFollowersScraped || 0
  
  // Calculate next scraping time based on scraping type
  let nextScrapingMinutes: number
  let nextScrapingTime: Date
  
  if (isContinuous) {
    // For continuous scraping, use 1-hour minimum break
    nextScrapingMinutes = 60
    nextScrapingTime = new Date(Date.now() + (60 * 60 * 1000))
  } else {
    // For standard scraping, use configured intervals
    nextScrapingMinutes = getRandomDelay(settings.scrapingIntervalMin, settings.scrapingIntervalMax)
    nextScrapingTime = new Date(Date.now() + (nextScrapingMinutes * 60 * 1000))
  }
  
  console.log(`🔍 SCRAPING: Progressive session completed!`)
  console.log(`   📊 Previous position: ${currentPosition} → New position: ${newPosition}`)
  console.log(`   📊 Followers scraped this session: ${newTotalScraped - totalScraped}`)
  console.log(`   📊 Total followers scraped: ${newTotalScraped}`)
  console.log(`   📊 Next scraping: ${nextScrapingTime.toISOString()} (in ${nextScrapingMinutes} minutes)`)
  
  // Update the API with the status and store next scraping time locally
  try {
    const scrapedThisSession = newTotalScraped - totalScraped
    // Update scraping timestamp based on type
    await ApiService.updateScrapingTimestamp(apiKey, {
      scrapingType: isContinuous ? 'continuous' : 'standard',
      followersScraped: scrapedThisSession,
      isComplete: true
    })
    
    await ApiService.updateExtensionStatus(apiKey, 'ACTIVE',
      `${isContinuous ? 'Continuous' : 'Progressive'} scraping: ${scrapedThisSession} new at position ${newPosition} - Next in ${nextScrapingMinutes}min`,
      true, {
        totalFollowersScraped: newTotalScraped,
        lastScrapedPosition: newPosition,
        allFollowersScraped: scrapedThisSession === 0 && newPosition === 0 // Reset cycle complete
      })
    
    // Store the next scraping time locally
    await Browser.storage.local.set({ 
      nextScrapingAllowedAt: nextScrapingTime.toISOString(),
      lastScrapingSession: new Date().toISOString()
    })
    
    const statusMessage = scrapedThisSession > 0
      ? `✅ ${isContinuous ? 'Continuous' : 'Progressive'} scraping: +${scrapedThisSession} followers (pos: ${newPosition}, total: ${newTotalScraped}) - Next in ${nextScrapingMinutes}min`
      : `✅ ${isContinuous ? 'Continuous' : 'Progressive'} scraping: Position reset (total: ${newTotalScraped}) - Next in ${nextScrapingMinutes}min`
    
    await setStatus(statusMessage)
    
    console.log(`🔍 SCRAPING: Next scraping time stored locally: ${nextScrapingTime.toISOString()}`)
  } catch (error) {
    console.error('🔍 SCRAPING: Error updating scraping status:', error)
  }
}

// This is the new, correct flow that replaces runScheduledScan
const runApiDrivenMessaging = async () => {
  if (scheduledScanIsAlive) {
    console.log('🚨 API messaging already running')
    return
  }

  // Double check that the extension is still supposed to be running
  if (!running) {
    console.log('🚨 Extension stopped before messaging started, aborting')
    return
  }

  scheduledScanIsAlive = true
  console.log('🚨 Starting API-driven messaging flow')

  try {
    const apiKeyResult = await Browser.storage.local.get(['apiKey'])
    const apiKey = apiKeyResult.apiKey
    
    if (!apiKey) {
      await setStatus('❌ No API key - Extension disabled')
      return
    }

    // Get Chrome extension settings for timing
    console.log('🚨 Fetching Chrome extension settings...')
    const settingsResult = await ApiService.getChromeExtensionSettings(apiKey)
    
    if (!settingsResult.success || !settingsResult.data) {
      console.error('🚨 CRITICAL: Failed to fetch settings from API. Stopping to prevent incorrect timing.')
      await setStatus('❌ Failed to fetch settings - Extension stopped')
      return
    }

    const settings = settingsResult.data

    console.log('🚨 Using timing settings from API:', {
      timeBetweenDMs: `${settings.timeBetweenDMsMin}-${settings.timeBetweenDMsMax} minutes`,
      messagesBeforeBreak: `${settings.messagesBeforeBreakMin}-${settings.messagesBeforeBreakMax} messages`,
      breakDuration: `${settings.breakDurationMin}-${settings.breakDurationMax} minutes`,
      naturalPause: `${settings.pauseStart} - ${settings.pauseStop}`,
      smartFocus: settings.smartFocus,
      scrapingInterval: `${settings.scrapingIntervalMin || 15}-${settings.scrapingIntervalMax || 30} minutes`,
      scrapingEnabled: settings.scrapingEnabled !== false ? true : false,
      nextScrapingAllowedAt: settings.nextScrapingAllowedAt,
      apiHasScrapingSettings: !!(settings.scrapingEnabled !== undefined)
    })

    // Check if we're in natural pause period
    if (isWithinNaturalPause(settings.pauseStart, settings.pauseStop)) {
      console.log('🚨 Currently in natural pause period, skipping messaging')
      await setStatus(`😴 Natural pause active (${settings.pauseStart} - ${settings.pauseStop})`)
      return
    }

    // 🔄 CONVERSATION_GATHERING: Check if we're still gathering conversations before trying to send messages
    const extensionStatusResult = await ApiService.getExtensionStatus(apiKey)
    if (extensionStatusResult.success && extensionStatusResult.data) {
      const currentStatus = extensionStatusResult.data.extensionStatus
      
      if (currentStatus === 'CONVERSATIONS_GATHERING') {
        console.log('🔄 CONVERSATION_GATHERING: Extension still gathering conversations, starting polling...')
        await setStatus('🔄 Waiting for conversation gathering to complete...')
        
        // Start polling for conversation gathering status if not already active
        if (!conversationGatheringPollingActive) {
          setTimeout(async () => {
            await pollConversationGatheringStatus(apiKey)
          }, 1000)
        }
        return
      } else if (currentStatus === 'SCRAPED_250') {
        console.log('🔄 CONVERSATION_GATHERING: Status is SCRAPED_250, starting conversation gathering...')

        // Update status to CONVERSATIONS_GATHERING and start polling
        await ApiService.updateExtensionStatus(apiKey, 'CONVERSATIONS_GATHERING',
          'Backend is processing Instagram conversations',
          true)

        // Start polling in the background
        setTimeout(async () => {
          await pollConversationGatheringStatus(apiKey)
        }, 2000)
        return
      } else if (currentStatus === 'CONVERSATIONS_GATHERED_READY') {
        console.log('🔄 CONVERSATION_GATHERING: Status is CONVERSATIONS_GATHERED_READY, transitioning to ACTIVE...')

        // Update status to ACTIVE to begin normal messaging operations
        await ApiService.updateExtensionStatus(apiKey, 'ACTIVE',
          'Ready for messaging - conversation gathering complete',
          true)

        await setStatus('✅ Ready for messaging - Starting attack list processing...')
        console.log('🔄 CONVERSATION_GATHERING: Extension status updated to ACTIVE - ready for messaging')

        // Continue with normal message processing (don't return, let it fall through)
      }
      
      console.log(`🚨 Current extension status: ${currentStatus}`)
    }

    console.log('🚨 Fetching messages to send from API...')
    const messagesToSendResult = await ApiService.getMessagesToSend(apiKey)
    
    // Check if daily limit has been reached
    if (messagesToSendResult.metadata?.dailyLimitReached) {
      console.log('🚨 Daily message limit reached:', messagesToSendResult.metadata)
      await setStatus('⏸️ Daily message limit reached - waiting for reset')
      
      // Still check for continuous scraping even if message limit reached
      console.log('🔍 SCRAPING: Daily limit reached, checking if continuous scraping is needed...')
      await checkAndPerformScraping(apiKey, settings)
      return
    }
    
    if (!messagesToSendResult.success || !messagesToSendResult.data || messagesToSendResult.data.length === 0) {
      console.log('🚨 No messages to send or API error:', messagesToSendResult.message)
      console.log('🔍 SCRAPING: 🎯 TRIGGER POINT 1 - No messages to send, checking scraping conditions...')
      
      // Check if we should do follower scraping when no messages to send
      await checkAndPerformScraping(apiKey, settings)
      
      await setStatus('✅ No messages ready to send')
      return
    }

    const messagesToSend = messagesToSendResult.data
    console.log(`🚨 Found ${messagesToSend.length} messages ready to send:`, messagesToSend)
    
    // Debug: Log all usernames - API should only return current Attack List users
    const usernames = messagesToSend.map(msg => msg.username)
    console.log('🚨 Usernames from API:', usernames)

    let tab = await getMainTab()
    console.log('🚨 Got main tab:', tab.id)

    // Retry loop to ensure content script is ready with better error handling
    let attempts = 0;
    let started = false;
    const maxAttempts = 8; // Increased attempts
    
    while (attempts < maxAttempts && !started && running) {
      try {
        console.log(`🔄 Attempting to connect to content script (attempt ${attempts + 1}/${maxAttempts})`);
        
        // First, check if tab is still valid and on Instagram
        const currentTab = await Browser.tabs.get(tab.id!);
        if (!currentTab.url?.includes('instagram.com')) {
          console.log('🔄 Tab navigated away from Instagram, getting fresh tab...');
          const freshTab = await getMainTab();
          tab = freshTab;
        }
        
        // Try to send the START_PROCESS message
        const response = await Browser.tabs.sendMessage(tab.id!, {
          type: MessageTypes.START_PROCESS,
          data: {},
        });
        
        started = true;
        console.log('✅ Connection to content script established successfully.', response);
        
      } catch (error: any) {
        attempts++;
        const isLastAttempt = attempts >= maxAttempts;
        
        console.warn(`❌ Could not connect to content script (attempt ${attempts}/${maxAttempts}):`, error.message);
        
        if (error.message?.includes('Receiving end does not exist')) {
          console.log('🔄 Content script not loaded yet, waiting longer...');
          await delay(5000); // Longer delay for content script loading
        } else if (error.message?.includes('The tab was closed')) {
          console.log('🔄 Tab was closed, getting fresh tab...');
          try {
            tab = await getMainTab();
            await delay(2000);
          } catch (tabError) {
            console.error('❌ Failed to get fresh tab:', tabError);
            if (isLastAttempt) break;
          }
        } else {
          console.log('🔄 Unknown connection error, retrying with standard delay...');
          await delay(3000);
        }
        
        // Force refresh the page if we're having persistent issues
        if (attempts === Math.floor(maxAttempts / 2)) {
          console.log('🔄 Mid-attempt refresh: Reloading Instagram tab to reset content script...');
          try {
            await Browser.tabs.reload(tab.id!);
            await delay(5000); // Wait for page to fully reload
          } catch (refreshError) {
            console.log('⚠️ Could not refresh tab:', refreshError);
          }
        }
      }
    }

    if (!started) {
      console.error('❌ Failed to establish connection with content script after all attempts');
      console.error('📊 Final state:', {
        attempts,
        maxAttempts,
        running,
        tabId: tab.id,
        tabUrl: tab.url
      });
      throw new Error(`Could not establish connection with content script after ${attempts} attempts. Content script may not be loaded or Instagram page may need refresh.`);
    }

    let messagesSent = 0
    let messagesSkipped = 0
    const messagesBeforeBreak = getRandomDelay(settings.messagesBeforeBreakMin, settings.messagesBeforeBreakMax)

    for (const messageToSend of messagesToSend) {
      if (!running) {
        console.log('🚨 Process stopped, breaking message loop')
        break
      }

      // Check if we need a break
      if (messagesSent > 0 && messagesSent % messagesBeforeBreak === 0) {
        const breakDuration = getRandomDelay(settings.breakDurationMin, settings.breakDurationMax)
        console.log(`🚨 Taking break after ${messagesSent} messages for ${breakDuration} minutes`)
        await setStatus(`😴 Break time: ${breakDuration} minutes after ${messagesSent} messages (monitoring for new followers)`)
        
        // During break time, monitor for new followers using Activity Inbox
        const breakStartTime = Date.now()
        const breakDurationMs = breakDuration * 60 * 1000 // Convert to milliseconds
        
        while (Date.now() - breakStartTime < breakDurationMs) {
          // Check for new followers during break time
          await checkActivityInboxForNewFollowers()
          
          // Wait 30 seconds before next check or until break time is complete
          const remainingBreak = breakDurationMs - (Date.now() - breakStartTime)
          const nextCheckDelay = Math.min(30000, remainingBreak) // 30 seconds or remaining time
          
          if (nextCheckDelay > 0) {
            await delay(nextCheckDelay)
          }
        }
      }

      // Add delay between DMs (except for the first message)
      if (messagesSent > 0) {
        const timeBetweenDMs = getRandomDelay(settings.timeBetweenDMsMin, settings.timeBetweenDMsMax)
        console.log(`🚨 Waiting ${timeBetweenDMs} minutes before next DM`)
        await setStatus(`⏳ Waiting ${timeBetweenDMs} minutes before next DM (monitoring for new followers)`)
        
        // During wait time, monitor for new followers using Activity Inbox
        const waitStartTime = Date.now()
        const waitDuration = timeBetweenDMs * 60 * 1000 // Convert to milliseconds
        
        while (Date.now() - waitStartTime < waitDuration) {
          // Check for new followers during wait time
          await checkActivityInboxForNewFollowers()
          
          // Wait 30 seconds before next check or until wait time is complete
          const remainingWait = waitDuration - (Date.now() - waitStartTime)
          const nextCheckDelay = Math.min(30000, remainingWait) // 30 seconds or remaining time
          
          if (nextCheckDelay > 0) {
            await delay(nextCheckDelay)
          }
        }
      }

      if (!running) {
        console.log('🚨 Process stopped during delay, breaking message loop')
        break
      }

      console.log(`🚨 Processing message for ${messageToSend.username}:`, messageToSend)
      
      try {
        // Step 0: Double-check contact status before sending (defensive programming)
        console.log(`🔍 Checking contact status for ${messageToSend.username} before sending...`)
        const contactStatusResult = await ApiService.getContactStatus(apiKey, messageToSend.id)
        
        if (!contactStatusResult.success) {
          console.log(`❌ Failed to check contact status for ${messageToSend.username}:`, contactStatusResult.message)
          messagesSkipped++
          continue
        }
        
        if (contactStatusResult.data?.stage === 'disqualified') {
          console.log(`⚠️ Contact ${messageToSend.username} is disqualified, skipping message`)
          messagesSkipped++
          continue
        }
        
        console.log(`✅ Contact ${messageToSend.username} status verified:`, contactStatusResult.data?.stage)
        
        // Step 1: Navigate to Profile
        await setStatus(`🧭 Navigating to profile: ${messageToSend.username}`)
        const profileUrl = `https://www.instagram.com/${messageToSend.username}/`
        await Browser.tabs.update(tab.id!, { url: profileUrl })
        await waitForTabToLoad(tab)
        await delay(4000)

        // Step 2: Click Message Button
        await setStatus(`🖱️ Clicking message button for ${messageToSend.username}`)
        await Browser.tabs.sendMessage(tab.id!, {
            type: MessageTypes.VISIT_USER_MESSAGES,
            data: { recipent_username: messageToSend.username }
        })
        await delay(6000) // Wait for DM page to load

        // Step 3: Send the message(s)
        const batchMessages = messageToSend.message.split(' | ').map(msg => msg.trim()).filter(msg => msg.length > 0)
        let batchSuccess = true
        let sentMessagesCount = 0

        for (let i = 0; i < batchMessages.length; i++) {
          const individualMessage = batchMessages[i]
          
          if (!running) {
            batchSuccess = false
            break
          }
          
          await setStatus(`📤 Sending message ${i + 1}/${batchMessages.length} to ${messageToSend.username}`)
          
          const sendResult = await Browser.tabs.sendMessage(tab.id!, {
            type: MessageTypes.MESSAGE_PROFILE,
            data: {
              recipent_id: messageToSend.username, // In this flow, this is the username
              text: individualMessage,
            },
          })

          if (sendResult === true) {
            sentMessagesCount++
            if (i < batchMessages.length - 1) {
              await delay(25000) // Wait between message lines
            }
          } else {
            batchSuccess = false
            break
          }
        }

        if (batchSuccess && sentMessagesCount === batchMessages.length) {
          // Mark message as sent - handle both batch and batch_sequence types
          if (messageToSend.type === 'batch') {
            // For batch messages, indicate completion to trigger follow-up creation
            const markSentData = {
              contactId: messageToSend.id,
              messageType: 'batch_sequence' as const,
              sequenceNumber: batchMessages.length, // Total number of messages sent
              batchCompleted: true // This triggers follow-up creation
            }
            console.log('🚨 Marking batch message as sent:', markSentData)
            await ApiService.markMessageSent(apiKey, markSentData)
            console.log('✅ Successfully marked batch message as sent')
          } else if (messageToSend.type === 'followup') {
            // For follow-up messages, mark the specific follow-up as sent
            const markSentData = {
              contactId: messageToSend.id,
              messageType: 'followup' as const,
              followUpId: messageToSend.followUpId,
              sequenceNumber: messageToSend.sequenceNumber
            }
            console.log('🚨 Marking follow-up message as sent:', markSentData)
            await ApiService.markMessageSent(apiKey, markSentData)
            console.log('✅ Successfully marked follow-up message as sent')
          } else {
            console.log('🚨 Unknown message type, not marking as sent:', messageToSend.type)
          }

          messagesSent++
        } else {
          messagesSkipped++
        }

      } catch (error) {
        console.error(`❌ Error processing message for ${messageToSend.username}:`, error)
        messagesSkipped++
      }
    }

    console.log(`🚨 API messaging completed - Sent: ${messagesSent}, Skipped: ${messagesSkipped}`)
    await setStatus(`✅ Completed - Sent: ${messagesSent}, Skipped: ${messagesSkipped}`)

    console.log('🔍 SCRAPING: 🎯 TRIGGER POINT 2 - Messaging complete, checking scraping conditions...')
    // After messaging is complete, check if we should do follower scraping
    await checkAndPerformScraping(apiKey, settings)

  } catch (error) {
    console.error('🚨 Error in API-driven messaging:', error)
    await setStatus(`❌ Error in messaging: ${error instanceof Error ? error.message : 'Unknown error'}`)
  } finally {
    scheduledScanIsAlive = false
  }
}


const setupScheduledScan = async () => {
  // Don't start a new scan if one is already running
  if (scheduledScanIsAlive) {
    console.log('🚨 Scheduled scan already running, skipping...')
    return
  }

  const config = await DataLayer.getConfig()
  
  // Check both config.power AND running state
  if (!config.power || !running) {
    console.log('🚨 Extension stopped (power:', config.power, 'running:', running, '), skipping scan')
    running = false
    return
  }

  await runApiDrivenMessaging() // Call the new main function
}

let lastScrapingCheckTime = 0;
const SCRAPING_CHECK_INTERVAL = 5 * 60 * 1000; // 5 minutes

const runScheduledScanHeartbeat = async () => {
  if (heartbeatScheduledAlreadyRunning) {
    return
  }
  heartbeatScheduledAlreadyRunning = true
  try {
    await setupScheduledScan()
    
    // 🔍 PERIODIC SCRAPING CHECK: Check for scraping opportunities, but not every heartbeat
    // Only check every 5 minutes to reduce API calls
    const now = Date.now();
    if (running && (now - lastScrapingCheckTime > SCRAPING_CHECK_INTERVAL)) {
      lastScrapingCheckTime = now;
      
      try {
        const apiKeyResult = await Browser.storage.local.get(['apiKey'])
        const apiKey = apiKeyResult.apiKey
        
        if (apiKey) {
          console.log('🔍 SCRAPING: 🎯 PERIODIC CHECK (5 min) - Checking scraping conditions...')
          
          // Get settings for scraping check
          const settingsResult = await ApiService.getChromeExtensionSettings(apiKey)
          if (settingsResult.success && settingsResult.data) {
            await checkAndPerformScraping(apiKey, settingsResult.data)
          }
        }
      } catch (error) {
        console.log('🔍 SCRAPING: Error in periodic scraping check:', error)
      }
    }
  } catch (error) {
    console.log('Error in heartbeat [scheduled] :: ', error)
  }
  heartbeatScheduledAlreadyRunning = false
}

const sendStopMessage = async () => {
    try {
        const main_tab = await getMainTab()
        await Browser.tabs.sendMessage(main_tab.id!, {
            type: MessageTypes.STOP_PROCESS,
        })
    } catch(e) {
        // ignore
    }
}

// Helper function to send followers in batches respecting API limits
const sendFollowersInBatches = async (apiKey: string, followers: any[], context: string, isInitialScraping: boolean = false) => {
  if (followers.length === 0) return

  const BATCH_SIZE = 10 // API limit for lastScrapedUsernames
  const batches = []
  
  // Split followers into batches
  for (let i = 0; i < followers.length; i += BATCH_SIZE) {
    batches.push(followers.slice(i, i + BATCH_SIZE))
  }

  console.log(`📦 ${context}: Processing ${followers.length} followers in ${batches.length} batches ${isInitialScraping ? '(INITIAL SCRAPING)' : ''}`)

  let previousBatchUsernames: string[] = []

  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i]
    console.log(`📦 ${context}: Processing batch ${i + 1}/${batches.length} (${batch.length} followers)`)

    const sendResult = await ApiService.sendScrapedFollowers(
      apiKey,
      batch,
      i * BATCH_SIZE, // startPosition
      followers.length, // totalFollowers
      i === batches.length - 1, // isComplete (true for last batch)
      previousBatchUsernames, // Send previous batch usernames for tracking
      isInitialScraping // Indicate if this is initial scraping
    )

    if (sendResult.success) {
      console.log(`✅ ${context}: Successfully sent batch ${i + 1} (${batch.length} followers) to API`)
      // Update previousBatchUsernames for next batch
      previousBatchUsernames = batch.map(f => f.instagramNickname).slice(-10) // Keep last 10
    } else {
      console.log(`❌ ${context}: Failed to send batch ${i + 1} to API:`, sendResult.message)
      // Continue with other batches even if one fails
    }

    // Add delay between batches to avoid overwhelming the API
    if (i < batches.length - 1) {
      console.log(`📦 ${context}: Waiting 2 seconds before next batch...`)
      await delay(2000)
    }
  }
}

// Function to check for new followers via Activity Inbox (MONITORING)
const checkActivityInboxForNewFollowers = async () => {
  if (followerCheckIsRunning) {
    console.log('📱 Activity inbox check already running, skipping...')
    return []
  }

  followerCheckIsRunning = true

  try {
    console.log('👀 MONITORING: Checking Activity Inbox for new followers...')

    // Get API key
    const apiKeyResult = await Browser.storage.local.get(['apiKey'])
    const apiKey = apiKeyResult.apiKey
    if (!apiKey) {
      console.log('No API key configured, skipping inbox check')
      return []
    }

    // Get main tab (don't create new tabs for monitoring)
    const tab = await getMainTab()
    
    // Get Instagram stories from activity inbox
    const inboxResult = await Browser.tabs.sendMessage(tab.id!, {
      type: MessageTypes.GET_INSTA_INBOX,
      data: { config: {} }
    })

    if (!inboxResult || inboxResult.status !== 'success') {
      console.log('📱 Failed to fetch Instagram inbox:', inboxResult?.message)
      return []
    }

    const stories = inboxResult.data || []
    console.log(`📱 Found ${stories.length} activity stories in inbox`)

    // Get already processed followers from storage to avoid duplicates
    const processedResult = await Browser.storage.local.get(['processedActivityFollowers'])
    const processedFollowers = new Set(processedResult.processedActivityFollowers || [])

    // Filter stories to only new followers after monitor start
    const newFollowers = []
    let skippedOldFollowers = 0
    let skippedProcessedFollowers = 0

    for (const story of stories) {
      const story_timestamp = story.timestamp * 1000 // Convert to milliseconds
      
      // Check if this follower is from after monitoring started (dmPastFollowers logic)
      if (story_timestamp < monitor_start_timestamp) {
        console.log(`📱 Skipping past follower ${story.username} (followed before monitoring started)`)
        skippedOldFollowers++
        continue
      }

      // Check if we've already processed this follower from Activity Inbox
      if (processedFollowers.has(story.username)) {
        console.log(`📱 Skipping already processed follower ${story.username} (already sent to API)`)
        skippedProcessedFollowers++
        continue
      }

      // This is a new follower since monitoring started and not yet processed
      newFollowers.push({
        instagramNickname: story.username,
        instagramId: story.id,
        isVerified: false,
        timestamp: story.timestamp,
        followedAt: new Date(story_timestamp)
      })
    }

    console.log(`📱 MONITORING RESULT: ${newFollowers.length} new followers, ${skippedOldFollowers} past followers skipped, ${skippedProcessedFollowers} already processed skipped`)

    // Send new followers to API if any (in batches to respect API limits)
    if (newFollowers.length > 0) {
      await sendFollowersInBatches(apiKey, newFollowers, 'MONITORING')
      
      // Mark these followers as processed to avoid reprocessing
      const newProcessedFollowers = newFollowers.map(f => f.instagramNickname)
      processedFollowers.forEach(username => newProcessedFollowers.push(username))
      
      // Keep only the last 1000 processed followers to avoid unbounded growth
      const limitedProcessedFollowers = newProcessedFollowers.slice(-1000)
      
      await Browser.storage.local.set({ 
        processedActivityFollowers: limitedProcessedFollowers 
      })
      
      console.log(`📱 Marked ${newFollowers.length} followers as processed (total tracked: ${limitedProcessedFollowers.length})`)
    }

    return newFollowers

  } catch (error) {
    console.error('📱 Error in activity inbox monitoring:', error)
    return []
  } finally {
    followerCheckIsRunning = false
  }
}

// Function to poll conversation gathering status and handle transitions
const pollConversationGatheringStatus = async (apiKey: string) => {
  if (conversationGatheringPollingActive) {
    console.log('🔄 CONVERSATION_GATHERING: Polling already active, skipping...')
    return
  }

  conversationGatheringPollingActive = true

  try {
    console.log('🔄 CONVERSATION_GATHERING: Starting conversation gathering status polling...')
    await setStatus('🔄 Checking conversation gathering progress...')

    // Keep polling until conversations are gathered or an error occurs
    let maxPollAttempts = 180 // 180 attempts * 10 seconds = 30 minutes max wait
    let pollAttempts = 0
    let gatheringComplete = false

    while (pollAttempts < maxPollAttempts && running && !gatheringComplete) {
      pollAttempts++
      
      try {
        console.log(`🔄 CONVERSATION_GATHERING: Poll attempt ${pollAttempts}/${maxPollAttempts}`)
        
        const gatheringResult = await ApiService.checkConversationGatheringStatus(apiKey)
        
        if (!gatheringResult.success) {
          console.log('🔄 CONVERSATION_GATHERING: API error checking status:', gatheringResult.message)
          await setStatus(`⚠️ Error checking conversation gathering: ${gatheringResult.message}`)
          
          // Wait before retrying
          await delay(10000)
          continue
        }

        const status = gatheringResult.data
        if (!status) {
          console.log('🔄 CONVERSATION_GATHERING: No status data received')
          await setStatus('⚠️ No conversation gathering status data')
          await delay(10000)
          continue
        }

        console.log('🔄 CONVERSATION_GATHERING: Status check result:', {
          isGathering: status.isGathering,
          isComplete: status.isComplete,
          progress: status.progress,
          estimatedTimeRemaining: status.estimatedTimeRemaining
        })

        if (status.error) {
          console.log('🔄 CONVERSATION_GATHERING: Error in gathering process:', status.error)
          await setStatus(`❌ Conversation gathering error: ${status.error}`)
          gatheringComplete = true // Exit polling on error
          break
        }

        if (status.isComplete) {
          console.log('🔄 CONVERSATION_GATHERING: ✅ Conversations gathering completed!')
          
          // Update extension status to CONVERSATIONS_GATHERED_READY
          try {
            await ApiService.updateExtensionStatus(apiKey, 'CONVERSATIONS_GATHERED_READY', 
              'All conversations processed - ready for attack list creation', 
              true)
            
            await setStatus('✅ All conversations processed - Ready for attack list creation!')
            gatheringComplete = true
            
            console.log('🔄 CONVERSATION_GATHERING: Extension status updated to CONVERSATIONS_GATHERED_READY')
          } catch (error) {
            console.error('🔄 CONVERSATION_GATHERING: Error updating extension status:', error)
            await setStatus('⚠️ Conversations processed but status update failed')
          }
          
          break
        }

        if (status.isGathering) {
          // Show progress if available
          let progressMessage = '🔄 Processing Instagram conversations'
          
          if (status.progress) {
            const { processedContacts, totalContacts, percentComplete } = status.progress
            progressMessage += ` (${processedContacts}/${totalContacts} - ${percentComplete}%)`
          }
          
          if (status.estimatedTimeRemaining) {
            progressMessage += ` - Est. ${status.estimatedTimeRemaining} remaining`
          }
          
          await setStatus(progressMessage)
          console.log(`🔄 CONVERSATION_GATHERING: ${progressMessage}`)
        } else {
          // Not gathering and not complete - may be waiting to start
          await setStatus('🔄 Waiting for conversation gathering to begin...')
          console.log('🔄 CONVERSATION_GATHERING: Waiting for gathering to start')
        }

        // Wait 10 seconds before next poll
        await delay(10000)

      } catch (error) {
        console.error('🔄 CONVERSATION_GATHERING: Error during polling:', error)
        await setStatus(`⚠️ Error polling conversation status: ${error instanceof Error ? error.message : 'Unknown error'}`)
        
        // Wait before retrying
        await delay(10000)
      }
    }

    if (pollAttempts >= maxPollAttempts && !gatheringComplete) {
      console.log('🔄 CONVERSATION_GATHERING: ⏰ Polling timeout reached')
      await setStatus('⏰ Conversation gathering timeout - Please check dashboard')
    }

  } catch (error) {
    console.error('🔄 CONVERSATION_GATHERING: Error in polling function:', error)
    await setStatus(`❌ Conversation gathering polling error: ${error instanceof Error ? error.message : 'Unknown error'}`)
  } finally {
    conversationGatheringPollingActive = false
    console.log('🔄 CONVERSATION_GATHERING: Polling completed')
  }
}

// Function to perform initial scraping of 250 most recent followers (FRESH_START only)
const performInitialScraping = async () => {
  if (followerCheckIsRunning) {
    console.log('🚀 INITIAL: Initial scraping already running, skipping...')
    return
  }

  // Check if initial scraping was already completed
  const localCheck = await Browser.storage.local.get(['initialScrapingCompleted'])
  if (localCheck.initialScrapingCompleted) {
    console.log('🚀 INITIAL: Initial scraping already completed previously, skipping...')
    await setStatus('✅ Initial scraping already completed')
    return
  }

  followerCheckIsRunning = true

  try {
    console.log('🚀 INITIAL: Starting initial scraping of 250 most recent followers...')
    await setStatus('🚀 Initial scraping: Getting 250 most recent followers...')
    
    // Clear any existing progressive scraping state to start fresh
    await Browser.storage.local.set({
      scrapingPosition: 0,
      totalFollowersScraped: 0,
      lastKnownFollowers: []
    })
    console.log('🚀 INITIAL: Cleared previous scraping state for fresh start')

    // Get API key
    const apiKeyResult = await Browser.storage.local.get(['apiKey'])
    const apiKey = apiKeyResult.apiKey
    if (!apiKey) {
      console.log('No API key configured, skipping initial scraping')
      return
    }

    // Get or create Instagram tab for scraping
    const instagramTabId = await DataLayer.getSecTabId()
    let tab = null
    
    if (instagramTabId) {
      try {
        tab = await Browser.tabs.get(parseInt(instagramTabId))
        if (!tab.url?.includes('instagram.com')) {
          await Browser.tabs.update(tab.id!, { url: 'https://www.instagram.com/' })
        }
      } catch (error) {
        tab = await Browser.tabs.create({
          url: 'https://www.instagram.com/',
          active: false,
        })
        await DataLayer.setSecTabId(tab.id!.toString())
      }
    } else {
      tab = await Browser.tabs.create({
        url: 'https://www.instagram.com/',
        active: false,
      })
      await DataLayer.setSecTabId(tab.id!.toString())
    }

    // 🔥 CRITICAL FIX: Ensure tab is active during scraping to prevent throttling
    if (tab && tab.id) {
      try {
        // Make tab active (focused) to prevent browser throttling
        await Browser.tabs.update(tab.id, { active: true })
        if (tab.windowId) {
          await Browser.windows.update(tab.windowId, { focused: true })
        }
        console.log('🚀 INITIAL: ✅ Tab made active to prevent throttling')
        
        // Wait for tab to become fully active
        await delay(1000)
      } catch (error) {
        console.log('🚀 INITIAL: ⚠️ Could not activate tab, continuing anyway:', error)
      }
    }

    await waitForTabToLoad(tab)
    await delay(3000)

    // Get user's profile username
    console.log('🚀 INITIAL: Getting logged-in user profile...')
    
    let username
    try {
      const profileLinkResult = await Browser.tabs.sendMessage(tab.id!, {
        type: MessageTypes.GET_PROFILE_URL
      })
      
      if (profileLinkResult) {
        username = profileLinkResult
        console.log('🚀 INITIAL: Found username:', username)
      } else {
        // Navigate to Instagram home and look for profile navigation
        await Browser.tabs.update(tab.id!, { url: 'https://www.instagram.com/' })
        await waitForTabToLoad(tab)
        await delay(3000)
        
        const profileNavResult = await Browser.tabs.sendMessage(tab.id!, {
          type: MessageTypes.GET_PROFILE_URL
        })
        
        if (profileNavResult) {
          username = profileNavResult
          console.log('🚀 INITIAL: Found username from home page:', username)
        }
      }
    } catch (error) {
      console.log('🚀 INITIAL: Error getting profile info:', error)
    }

    if (!username) {
      console.log('🚀 INITIAL: Could not determine logged-in user profile, trying to click profile element')

      // Try clicking profile element with "Profil" or "Profile" text
      try {
        const profileClickResult = await Browser.tabs.sendMessage(tab.id!, {
          type: MessageTypes.CLICK_PROFILE_ELEMENT
        })

        if (profileClickResult && profileClickResult.status === 'success') {
          username = profileClickResult.username
          console.log('🚀 INITIAL: Successfully clicked profile element, found username:', username)
        } else {
          console.log('🚀 INITIAL: Failed to click profile element:', profileClickResult?.message)
          await setStatus('❌ Could not find or click profile element - initial scraping failed')
          return
        }
      } catch (error) {
        console.log('🚀 INITIAL: Error clicking profile element:', error)
        await setStatus('❌ Error clicking profile element - initial scraping failed')
        return
      }
    }

    // Navigate to user's profile if not already there
    if (!tab.url?.includes(`/${username}/`)) {
      console.log('🚀 INITIAL: Not on profile page, trying to click profile element')

      try {
        const profileClickResult = await Browser.tabs.sendMessage(tab.id!, {
          type: MessageTypes.CLICK_PROFILE_ELEMENT
        })

        if (profileClickResult && profileClickResult.status === 'success') {
          console.log('🚀 INITIAL: Successfully navigated to profile via clicking')
        } else {
          console.log('🚀 INITIAL: Profile click failed, falling back to direct navigation')
          await Browser.tabs.update(tab.id!, {
            url: `https://www.instagram.com/${username}/`
          })
          await waitForTabToLoad(tab)
          await delay(3000)
        }
      } catch (error) {
        console.log('🚀 INITIAL: Error with profile click, falling back to direct navigation:', error)
        await Browser.tabs.update(tab.id!, {
          url: `https://www.instagram.com/${username}/`
        })
        await waitForTabToLoad(tab)
        await delay(3000)
      }
    }

    // Open followers panel
    const openResult = await Browser.tabs.sendMessage(tab.id!, {
      type: MessageTypes.OPEN_FOLLOWERS_PANEL,
      data: { username }
    })

    if (openResult.status !== 'success') {
      console.log('🚀 INITIAL: Failed to open followers panel:', openResult.message)
      await setStatus('❌ Failed to open followers panel - initial scraping failed')
      return
    }

    // 🚀 INITIAL SCRAPING: Always get 250 most recent followers (skipCount = 0)
    await setStatus('🚀 Scraping 250 most recent followers...')
    const scrapingResult = await Browser.tabs.sendMessage(tab.id!, {
      type: MessageTypes.GET_FOLLOWERS_LIST_BY_COUNT,
      data: { 
        count: 250,
        skipCount: 0 // Always start from the beginning for initial scraping
      }
    })

    if (!scrapingResult || !scrapingResult.followers || !Array.isArray(scrapingResult.followers)) {
      console.log('🚀 INITIAL: Failed to scrape followers')
      await setStatus('❌ Failed to scrape followers - initial scraping failed')
      return
    }

    const scrapedFollowers = scrapingResult.followers
    console.log(`🚀 INITIAL: Successfully scraped ${scrapedFollowers.length} followers`)

    // Convert to API format
    const initialFollowers = []
    for (const followerUrl of scrapedFollowers) {
      const username = followerUrl.split('/').filter(Boolean).pop()
      if (username) {
        initialFollowers.push({
          instagramNickname: username,
          isVerified: false
        })
      }
    }

    console.log(`🚀 INITIAL: Processed ${initialFollowers.length} followers for API submission`)

    // Send initial followers to API
    if (initialFollowers.length > 0) {
      await setStatus(`🚀 Sending ${initialFollowers.length} initial followers to API...`)
      await sendFollowersInBatches(apiKey, initialFollowers, 'INITIAL_SCRAPING', true)
      
      // Store these as baseline followers
      const storedFollowers = initialFollowers.map(f => ({ username: f.instagramNickname })).slice(0, 500)
      await Browser.storage.local.set({ 
        lastKnownFollowers: storedFollowers,
        scrapingPosition: 250, // Set position after initial scraping
        totalFollowersScraped: initialFollowers.length,
        lastScrapingSession: new Date().toISOString(),
        initialScrapingCompleted: true
      })
      
      console.log(`🚀 INITIAL: Stored ${storedFollowers.length} followers as baseline`)
      
      // Update extension status to indicate initial scraping is complete
      try {
        await ApiService.updateExtensionStatus(apiKey, 'SCRAPED_250', 
          `Initial scraping completed: ${initialFollowers.length} followers`, 
          true, {
            totalFollowersScraped: initialFollowers.length,
            lastScrapedPosition: 250,
            allFollowersScraped: false
          })
        
        console.log('🚀 INITIAL: ✅ Updated extension status to SCRAPED_250')
        await setStatus(`✅ Initial scraping complete: ${initialFollowers.length} followers sent to API`)
        
        // 🔄 CONVERSATION_GATHERING: Start polling for conversation gathering after initial scraping
        console.log('🔄 CONVERSATION_GATHERING: Starting conversation gathering polling after initial scraping...')
        
        // Update status to CONVERSATIONS_GATHERING and start polling
        await ApiService.updateExtensionStatus(apiKey, 'CONVERSATIONS_GATHERING', 
          'Backend is processing Instagram conversations', 
          true)
        
        // Start polling in the background
        setTimeout(async () => {
          await pollConversationGatheringStatus(apiKey)
        }, 2000) // Small delay to let status update propagate
        
      } catch (error) {
        console.error('🚀 INITIAL: Error updating extension status:', error)
        await setStatus(`⚠️ Initial scraping complete but status update failed: ${initialFollowers.length} followers`)
      }
    } else {
      console.log('🚀 INITIAL: No followers found during initial scraping')
      await setStatus('⚠️ Initial scraping found no followers')
      
      // Still update status to indicate scraping was attempted
      try {
        await ApiService.updateExtensionStatus(apiKey, 'ACTIVE', 
          'Initial scraping found no followers', 
          true, {
            totalFollowersScraped: 0,
            lastScrapedPosition: 0,
            allFollowersScraped: false
          })
      } catch (error) {
        console.error('🚀 INITIAL: Error updating extension status for empty result:', error)
      }
    }

  } catch (error) {
    console.error('🚀 INITIAL: Error in initial follower scraping:', error)
    await setStatus('❌ Error during initial scraping')
  } finally {
    followerCheckIsRunning = false
    console.log('🚀 INITIAL: Initial scraping completed')
  }
}

// Function to check for new followers via Follower Panel (SCRAPING)  
const checkForNewFollowers = async () => {
  if (followerCheckIsRunning) {
    console.log('🔍 Follower scraping already running, skipping...')
    return
  }

  followerCheckIsRunning = true

  try {
    console.log('🔍 SCRAPING: Starting follower panel scraping...')

    // Get API key
    const apiKeyResult = await Browser.storage.local.get(['apiKey'])
    const apiKey = apiKeyResult.apiKey
    if (!apiKey) {
      console.log('No API key configured, skipping follower scraping')
      return
    }

    // Get or create Instagram tab for scraping
    const instagramTabId = await DataLayer.getSecTabId()
    let tab = null
    
    if (instagramTabId) {
      try {
        tab = await Browser.tabs.get(parseInt(instagramTabId))
        if (!tab.url?.includes('instagram.com')) {
          await Browser.tabs.update(tab.id!, { url: 'https://www.instagram.com/' })
        }
      } catch (error) {
        // Tab was closed, create a new one
        tab = await Browser.tabs.create({
          url: 'https://www.instagram.com/',
          active: false,
        })
        await DataLayer.setSecTabId(tab.id!.toString())
      }
    } else {
      tab = await Browser.tabs.create({
        url: 'https://www.instagram.com/',
        active: false,
      })
      await DataLayer.setSecTabId(tab.id!.toString())
    }

    // Wait for tab to load
    await waitForTabToLoad(tab)
    await delay(3000)

    // Get user's profile username
    console.log('🔍 SCRAPING_DEBUG: Requesting profile URL from tab:', tab.id, 'URL:', tab.url)
    
    let username
    try {
      username = await Browser.tabs.sendMessage(tab.id!, {
        type: MessageTypes.GET_PROFILE_URL
      })
      console.log('🔍 SCRAPING_DEBUG: Profile URL response:', username)
    } catch (error) {
      console.log('🔍 SCRAPING_DEBUG: Error getting profile URL:', error)
      username = null
    }

    if (!username) {
      console.log('🔍 SCRAPING_DEBUG: Could not get profile URL, trying to click profile element')

      // Try clicking profile element with "Profil" or "Profile" text
      try {
        const profileClickResult = await Browser.tabs.sendMessage(tab.id!, {
          type: MessageTypes.CLICK_PROFILE_ELEMENT
        })

        if (profileClickResult && profileClickResult.status === 'success') {
          username = profileClickResult.username
          console.log('🔍 SCRAPING_DEBUG: Successfully clicked profile element, found username:', username)
        } else {
          console.log('🔍 SCRAPING_DEBUG: Failed to click profile element, skipping follower scraping:', profileClickResult?.message)
          return
        }
      } catch (error) {
        console.log('🔍 SCRAPING_DEBUG: Error clicking profile element, skipping follower scraping:', error)
        return
      }
    }

    // Navigate to user's profile if not already there
    if (!tab.url?.includes(`/${username}/`)) {
      console.log('🔍 SCRAPING_DEBUG: Not on profile page, trying to click profile element')

      try {
        const profileClickResult = await Browser.tabs.sendMessage(tab.id!, {
          type: MessageTypes.CLICK_PROFILE_ELEMENT
        })

        if (profileClickResult && profileClickResult.status === 'success') {
          console.log('🔍 SCRAPING_DEBUG: Successfully navigated to profile via clicking')
        } else {
          console.log('🔍 SCRAPING_DEBUG: Profile click failed, falling back to direct navigation')
          await Browser.tabs.update(tab.id!, {
            url: `https://www.instagram.com/${username}/`
          })
          await waitForTabToLoad(tab)
          await delay(3000)
        }
      } catch (error) {
        console.log('🔍 SCRAPING_DEBUG: Error with profile click, falling back to direct navigation:', error)
        await Browser.tabs.update(tab.id!, {
          url: `https://www.instagram.com/${username}/`
        })
        await waitForTabToLoad(tab)
        await delay(3000)
      }
    }

    // Open followers panel
    const openResult = await Browser.tabs.sendMessage(tab.id!, {
      type: MessageTypes.OPEN_FOLLOWERS_PANEL,
      data: { username }
    })

    if (openResult.status !== 'success') {
      console.log('Failed to open followers panel:', openResult.message)
      return
    }

    // Get current stored followers
    const storedFollowersResult = await Browser.storage.local.get(['lastKnownFollowers'])
    const lastKnownFollowers = storedFollowersResult.lastKnownFollowers || []
    const lastKnownUsernames = new Set(lastKnownFollowers.map((f: any) => f.username))

    // Get the current scraping position from local storage
    const scrapingProgressResult = await Browser.storage.local.get(['scrapingPosition', 'totalFollowersScraped'])
    const currentPosition = scrapingProgressResult.scrapingPosition || 0
    const totalScraped = scrapingProgressResult.totalFollowersScraped || 0
    
    console.log(`🔍 SCRAPING: Current position: ${currentPosition}, Total scraped: ${totalScraped}`)

    // Progressive scraping: get next 250 followers starting from current position
    const scrapingResult = await Browser.tabs.sendMessage(tab.id!, {
      type: MessageTypes.GET_FOLLOWERS_LIST_BY_COUNT,
      data: { 
        count: 250,
        skipCount: currentPosition 
      }
    })

    if (!scrapingResult || !scrapingResult.followers || !Array.isArray(scrapingResult.followers)) {
      console.log('Failed to scrape followers from panel')
      return
    }

    const { followers: scrapedFollowers, totalPosition, skippedCount, collectedCount } = scrapingResult
    console.log(`🔍 SCRAPING: Progressive result - Skipped: ${skippedCount}, Collected: ${collectedCount}, New position: ${totalPosition}`)

    // Convert scraped followers to the format expected by API
    const newFollowers = []
    for (const followerUrl of scrapedFollowers) {
      const username = followerUrl.split('/').filter(Boolean).pop()
      if (username) {
        newFollowers.push({
          instagramNickname: username,
          isVerified: false
        })
      }
    }

    console.log(`🔍 SCRAPING: Found ${newFollowers.length} new followers via progressive panel scraping`)

    // Send new followers to API if any (in batches to respect API limits)
    if (newFollowers.length > 0) {
      console.log(`🔍 SCRAPING: About to call sendFollowersInBatches with ${newFollowers.length} followers`)
      await sendFollowersInBatches(apiKey, newFollowers, 'PROGRESSIVE_SCRAPING')
      
      // Update scraping position for next session
      await Browser.storage.local.set({ 
        scrapingPosition: totalPosition,
        totalFollowersScraped: totalScraped + newFollowers.length,
        lastScrapingSession: new Date().toISOString()
      })
      
      console.log(`🔍 SCRAPING: Updated position to ${totalPosition}, total scraped: ${totalScraped + newFollowers.length}`)
      
      // Update stored followers (keep last 500 to prevent storage overflow)
      const updatedFollowers = [
        ...newFollowers.map(f => ({ username: f.instagramNickname })),
        ...lastKnownFollowers
      ].slice(0, 500)
      
      await Browser.storage.local.set({ lastKnownFollowers: updatedFollowers })
    } else {
      console.log(`🔍 SCRAPING: No new followers found at position ${currentPosition}`)
      
      // If no new followers found, we might have reached the end or hit a limit
      // Reset position to start over from the beginning for the next session
      if (currentPosition > 0) {
        console.log(`🔍 SCRAPING: Resetting position to 0 for next session (may have reached end)`)
        await Browser.storage.local.set({ 
          scrapingPosition: 0,
          lastScrapingSession: new Date().toISOString()
        })
      }
    }

  } catch (error) {
    console.error('🔍 Error in follower panel scraping:', error)
  } finally {
    followerCheckIsRunning = false
  }
}

Browser.runtime.onMessage.addListener(async (message, sender) => {
  // Only log non-status messages to reduce console spam
  if (message.type !== MessageTypes.GET_STATUS && 
      message.type !== MessageTypes.GET_API_KEY && 
      message.type !== MessageTypes.GET_API_STATUS) {
    console.log('Bg :: Message :: ', message)
  }

  await createHeartbeatAlarm()

  if (message.type === MessageTypes.START_PROCESS) {
    console.log('Bg :: START_PROCESS :: ', message)
    let config = await DataLayer.getConfig()
    await DataLayer.setConfig({
      ...config,
      power: true,
    })
    running = true
    monitor_start_timestamp = Date.now() // Set monitoring start time
    console.log('🚨 Monitor start timestamp set:', new Date(monitor_start_timestamp).toISOString())
    
    // Clear processed followers list when monitoring starts fresh
    await Browser.storage.local.set({ processedActivityFollowers: [] })
    console.log('🚨 Cleared processed activity followers list for fresh monitoring start')
    
    // 🔥 FIX: Check extension status from API before starting scraping
    try {
      const apiKeyResult = await Browser.storage.local.get(['apiKey'])
      const apiKey = apiKeyResult.apiKey
      
      if (apiKey) {
        console.log('🔍 STARTUP: Checking extension status from API...')
        const statusResult = await ApiService.getExtensionStatus(apiKey)
        
        if (statusResult.success && statusResult.data) {
          const apiStatus = statusResult.data
          console.log('🔍 STARTUP: API extension status:', {
            extensionStatus: apiStatus.extensionStatus,
            totalFollowersScraped: apiStatus.totalFollowersScraped,
            lastScrapedPosition: apiStatus.lastScrapedPosition,
            allFollowersScraped: apiStatus.allFollowersScraped
          })
          
          // Update local scraping position from API
          if (apiStatus.lastScrapedPosition !== undefined) {
            await Browser.storage.local.set({ 
              scrapingPosition: apiStatus.lastScrapedPosition,
              totalFollowersScraped: apiStatus.totalFollowersScraped || 0
            })
            console.log('🔍 STARTUP: Updated local scraping position from API:', apiStatus.lastScrapedPosition)
          }
          
          // Only perform initial scraping if in FRESH_START status
          if (apiStatus.extensionStatus === 'FRESH_START') {
            console.log('🔍 STARTUP: FRESH_START status detected, will perform initial scraping')
            setTimeout(async () => {
              await performInitialScraping()
            }, 5000) // Delay to let extension fully initialize
          } else if (apiStatus.extensionStatus === 'CONVERSATIONS_GATHERING') {
            console.log('🔄 STARTUP: CONVERSATIONS_GATHERING status detected, will start polling')
            setTimeout(async () => {
              await pollConversationGatheringStatus(apiKey)
            }, 5000) // Delay to let extension fully initialize
          } else {
            console.log('🔍 STARTUP: Extension status is', apiStatus.extensionStatus, '- skipping initial scraping')
          }
        } else {
          console.log('🔍 STARTUP: Could not fetch extension status from API - skipping scraping until status is confirmed')
          // If we can't get status, don't assume anything - wait for explicit confirmation
          await setStatus('⚠️ Could not verify extension status - scraping disabled')
        }
      } else {
        console.log('🔍 STARTUP: No API key, skipping status check')
      }
    } catch (error) {
      console.error('🔍 STARTUP: Error checking initial status:', error)
    }
    
    return true
  } else if (message.type === MessageTypes.STOP_PROCESS) {
    console.log('Bg :: STOP_PROCESS :: ', message)
    let config = await DataLayer.getConfig()
    await DataLayer.setConfig({
      ...config,
      power: false,
      status: '',
    })
    running = false
    scheduledScanIsAlive = false // Force stop any ongoing messaging
    await sendStopMessage()
    return true
  } else if (message.type === MessageTypes.GET_COOKIE) {
    let cookie = await Browser.cookies.get({ url: message.data.url, name: message.data.name })
    return cookie?.value
  } else if (message.type === MessageTypes.SAVE_SETTINGS) {
    console.log('Bg :: SAVE_SETTINGS :: ', message)
    let config = await DataLayer.getConfig()
    await DataLayer.setConfig({
      ...config,
      ...message.data,
    })
    return true
  } else if (message.type === MessageTypes.GET_SETTINGS) {
    console.log('Bg :: GET_SETTINGS :: ', message)
    let config = await DataLayer.getConfig()
    return config
  } else if (message.type === MessageTypes.GET_STATUS) {
    // Don't log GET_STATUS to reduce console spam
    let config = await DataLayer.getConfig()
    return {
      ...config,
      power: running,
      status: (await DataLayer.getConfig()).status,
    }
  } else if (message.type === MessageTypes.SAVE_API_KEY) {
    console.log('Bg :: SAVE_API_KEY :: ', message)
    await Browser.storage.local.set({ apiKey: message.data.apiKey })
    return true
  } else if (message.type === MessageTypes.GET_API_KEY) {
    // Don't log GET_API_KEY to reduce console spam
    const result = await Browser.storage.local.get(['apiKey'])
    return result.apiKey || ''
  } else if (message.type === MessageTypes.VERIFY_API_KEY) {
    console.log('Bg :: VERIFY_API_KEY :: ', message)
    try {
      const result = await ApiService.verifyApiKey(message.data.apiKey)
      const apiStatus = {
        isAuthenticated: result.success,
        organizationName: result.data?.organizationName,
        organizationSlug: result.data?.organizationSlug,
        lastVerified: new Date().toISOString(),
        error: result.success ? undefined : result.message
      }
      await Browser.storage.local.set({ apiStatus })
      return apiStatus
    } catch (error) {
      console.error('Error verifying API key:', error)
      const apiStatus = {
        isAuthenticated: false,
        error: 'Failed to verify API key'
      }
      await Browser.storage.local.set({ apiStatus })
      return apiStatus
    }
  } else if (message.type === MessageTypes.GET_API_STATUS) {
    // Don't log GET_API_STATUS to reduce console spam
    const result = await Browser.storage.local.get(['apiStatus'])
    return result.apiStatus || { isAuthenticated: false }
  } else if (message.type === MessageTypes.RESET_SCRAPING_PROGRESS) {
      console.log('Bg :: RESET_SCRAPING_PROGRESS :: ', message)
      
      // Reset progressive scraping position to start from beginning
      await Browser.storage.local.set({
        scrapingPosition: 0,
        totalFollowersScraped: 0,
        lastScrapingSession: new Date().toISOString()
      })
      
      console.log('🔍 SCRAPING: Progressive scraping position reset to 0')
      await setStatus('✅ Progressive scraping position reset - will start from beginning')
      
      return { success: true, message: 'Progressive scraping position reset to start from the beginning.' }
  } else if (message.type === MessageTypes.CLEAR_SCRAPING_WAIT) {
      console.log('Bg :: CLEAR_SCRAPING_WAIT :: ', message)
      
      // Clear the local storage nextScrapingAllowedAt to sync with API reset
      await Browser.storage.local.remove(['nextScrapingAllowedAt'])
      
      console.log('🔍 SCRAPING: Local storage nextScrapingAllowedAt cleared - synced with API reset')
      await setStatus('✅ Scraping wait period cleared - ready to scrape')
      
      return { success: true, message: 'Local scraping wait period cleared successfully.' }
  } else if (message.type === MessageTypes.START_FOLLOWER_SCRAPING) {
      console.log('Bg :: START_FOLLOWER_SCRAPING :: Manual trigger from popup')
      
      // Manual trigger for scraping check - get API key and settings
      try {
        const apiKeyResult = await Browser.storage.local.get(['apiKey'])
        const apiKey = apiKeyResult.apiKey
        
        if (!apiKey) {
          await setStatus('❌ No API key - Cannot check scraping')
          return { success: false, message: 'No API key configured' }
        }

        console.log('🔍 SCRAPING: 🎯 MANUAL TRIGGER - Fetching settings and checking scraping conditions...')
        const settingsResult = await ApiService.getChromeExtensionSettings(apiKey)
        
        if (!settingsResult.success || !settingsResult.data) {
          await setStatus('❌ Failed to fetch settings - Cannot check scraping')
          return { success: false, message: 'Failed to fetch settings from API' }
        }

        const settings = settingsResult.data
        console.log('🔍 SCRAPING: 🎯 MANUAL TRIGGER - Got settings, running scraping check...')
        
        // Trigger the scraping check manually
        await checkAndPerformScraping(apiKey, settings)
        
        return { success: true, message: 'Scraping check triggered successfully' }
      } catch (error) {
        console.error('🔍 SCRAPING: Error in manual scraping trigger:', error)
        await setStatus('❌ Error checking scraping conditions')
        return { success: false, message: 'Error checking scraping conditions' }
      }
  } else if (message.type === MessageTypes.CHECK_CONVERSATION_GATHERING_STATUS) {
      console.log('Bg :: CHECK_CONVERSATION_GATHERING_STATUS :: Manual check from popup')
      
      try {
        const apiKeyResult = await Browser.storage.local.get(['apiKey'])
        const apiKey = apiKeyResult.apiKey
        
        if (!apiKey) {
          return { success: false, message: 'No API key configured' }
        }

        const result = await ApiService.checkConversationGatheringStatus(apiKey)
        return result
      } catch (error) {
        console.error('🔄 CONVERSATION_GATHERING: Error checking status:', error)
        return { success: false, message: 'Error checking conversation gathering status' }
      }
  } else if (message.type === MessageTypes.POLL_CONVERSATION_GATHERING) {
      console.log('Bg :: POLL_CONVERSATION_GATHERING :: Manual poll trigger from popup')
      
      try {
        const apiKeyResult = await Browser.storage.local.get(['apiKey'])
        const apiKey = apiKeyResult.apiKey
        
        if (!apiKey) {
          await setStatus('❌ No API key - Cannot poll conversation gathering')
          return { success: false, message: 'No API key configured' }
        }

        // Start polling in the background
        if (!conversationGatheringPollingActive) {
          setTimeout(async () => {
            await pollConversationGatheringStatus(apiKey)
          }, 1000)
          
          return { success: true, message: 'Conversation gathering polling started' }
        } else {
          return { success: false, message: 'Conversation gathering polling already active' }
        }
      } catch (error) {
        console.error('🔄 CONVERSATION_GATHERING: Error starting polling:', error)
        await setStatus('❌ Error starting conversation gathering polling')
        return { success: false, message: 'Error starting conversation gathering polling' }
      }
  }
})

Browser.alarms.onAlarm.addListener(async (alarm) => {
  if (alarm.name === 'heartbeat') {
    console.log('Heartbeat received')
    setTimeout(async () => {
      await runScheduledScanHeartbeat()
    }, 1)
  } else if (alarm.name === 'followUpCheck') {
    // This can be re-implemented if follow-ups are needed
  // Note: newFollowersCheck alarm removed - now monitoring during wait times instead
  }
})
