/**
 * Optimized Follower Scraping Module
 * Minimizes tab activation time while maintaining reliability
 */

import Browser from 'webextension-polyfill'
import { MessageTypes } from '@shared/messaging'

interface ScrapingOptions {
  batchSize: number
  maxActiveDuration: number // Maximum milliseconds tab stays active
  retryOnBackground: boolean // Try background first before activating
}

/**
 * Smart tab activation manager
 * Tracks previous active tab and restores it after scraping
 */
class TabActivationManager {
  private previousActiveTab: Browser.Tabs.Tab | null = null
  private scrapingTabId: number | null = null
  private activationTimer: NodeJS.Timeout | null = null

  async activateForScraping(tabId: number, maxDuration: number): Promise<void> {
    // Store current active tab
    const tabs = await Browser.tabs.query({ active: true, currentWindow: true })
    this.previousActiveTab = tabs[0] || null
    this.scrapingTabId = tabId

    // Activate scraping tab
    await Browser.tabs.update(tabId, { active: true })
    
    // Set safety timer to restore previous tab
    this.activationTimer = setTimeout(() => {
      this.restorePreviousTab()
    }, maxDuration)
  }

  async restorePreviousTab(): Promise<void> {
    if (this.activationTimer) {
      clearTimeout(this.activationTimer)
      this.activationTimer = null
    }

    if (this.previousActiveTab?.id) {
      try {
        await Browser.tabs.update(this.previousActiveTab.id, { active: true })
      } catch (error) {
        console.log('Could not restore previous tab:', error)
      }
    }

    this.previousActiveTab = null
    this.scrapingTabId = null
  }
}

/**
 * Optimized scraping coordinator
 */
export class OptimizedScraper {
  private tabManager = new TabActivationManager()
  private lastScrapingTime = 0
  private minTimeBetweenScrapes = 60000 // 1 minute minimum between scrapes

  /**
   * Attempt to scrape in background first, activate if needed
   */
  async adaptiveScrape(
    tabId: number,
    username: string,
    options: ScrapingOptions = {
      batchSize: 50,
      maxActiveDuration: 5000,
      retryOnBackground: true
    }
  ): Promise<any[]> {
    console.log('🔍 OPTIMIZED: Starting adaptive scraping...')

    // Check if enough time has passed since last scraping
    const timeSinceLastScrape = Date.now() - this.lastScrapingTime
    if (timeSinceLastScrape < this.minTimeBetweenScrapes) {
      console.log(`🔍 OPTIMIZED: Too soon since last scrape (${timeSinceLastScrape}ms), skipping`)
      return []
    }

    let followers: any[] = []

    // Step 1: Try background scraping first if enabled
    if (options.retryOnBackground) {
      console.log('🔍 OPTIMIZED: Attempting background scraping...')
      followers = await this.attemptBackgroundScraping(tabId, username, options.batchSize)
      
      if (followers.length > 0) {
        console.log(`🔍 OPTIMIZED: ✅ Background scraping succeeded! Got ${followers.length} followers`)
        this.lastScrapingTime = Date.now()
        return followers
      }
      
      console.log('🔍 OPTIMIZED: ⚠️ Background scraping failed, switching to active tab method')
    }

    // Step 2: Use active tab scraping
    followers = await this.activeTabScraping(tabId, username, options)
    this.lastScrapingTime = Date.now()
    
    return followers
  }

  /**
   * Try to scrape without activating the tab
   */
  private async attemptBackgroundScraping(
    tabId: number,
    username: string,
    batchSize: number
  ): Promise<any[]> {
    try {
      // Check if tab is already visible
      const tab = await Browser.tabs.get(tabId)
      if (!tab.active) {
        console.log('🔍 OPTIMIZED: Tab is not active, attempting background scrape...')
      }

      // Get current position
      const progressResult = await Browser.storage.local.get(['scrapingPosition'])
      const currentPosition = progressResult.scrapingPosition || 0

      // Try to open followers panel if not already open
      const openResult = await Browser.tabs.sendMessage(tabId, {
        type: MessageTypes.OPEN_FOLLOWERS_PANEL,
        data: { username }
      })

      if (openResult.status !== 'success') {
        return []
      }

      // Attempt scraping with smaller batch
      const scrapingResult = await Browser.tabs.sendMessage(tabId, {
        type: MessageTypes.GET_FOLLOWERS_LIST_BY_COUNT,
        data: { 
          count: Math.min(batchSize, 25), // Smaller batch for background
          skipCount: currentPosition
        }
      })

      if (scrapingResult?.followers?.length > 0) {
        return scrapingResult.followers
      }

      return []
    } catch (error) {
      console.log('🔍 OPTIMIZED: Background scraping error:', error)
      return []
    }
  }

  /**
   * Scrape with minimal tab activation time
   */
  private async activeTabScraping(
    tabId: number,
    username: string,
    options: ScrapingOptions
  ): Promise<any[]> {
    console.log(`🔍 OPTIMIZED: Starting active tab scraping (max ${options.maxActiveDuration}ms)...`)

    try {
      // Activate tab with automatic restoration
      await this.tabManager.activateForScraping(tabId, options.maxActiveDuration)

      // Get current position
      const progressResult = await Browser.storage.local.get(['scrapingPosition'])
      const currentPosition = progressResult.scrapingPosition || 0

      // Quick operations while tab is active
      const startTime = Date.now()

      // Open followers panel
      const openResult = await Browser.tabs.sendMessage(tabId, {
        type: MessageTypes.OPEN_FOLLOWERS_PANEL,
        data: { username }
      })

      if (openResult.status !== 'success') {
        await this.tabManager.restorePreviousTab()
        return []
      }

      // Scrape with optimized batch size
      const scrapingResult = await Browser.tabs.sendMessage(tabId, {
        type: MessageTypes.GET_FOLLOWERS_LIST_BY_COUNT,
        data: { 
          count: options.batchSize,
          skipCount: currentPosition
        }
      })

      const elapsed = Date.now() - startTime
      console.log(`🔍 OPTIMIZED: Scraping completed in ${elapsed}ms`)

      // Restore previous tab
      await this.tabManager.restorePreviousTab()

      return scrapingResult?.followers || []
    } catch (error) {
      console.error('🔍 OPTIMIZED: Active tab scraping error:', error)
      await this.tabManager.restorePreviousTab()
      return []
    }
  }

  /**
   * Check if user is idle before scraping
   */
  async isGoodTimeToScrape(): Promise<boolean> {
    // Check Chrome idle state if available
    if (Browser.idle) {
      const idleState = await Browser.idle.queryState(60) // 60 seconds
      if (idleState === 'active') {
        console.log('🔍 OPTIMIZED: User is active, postponing scraping')
        return false
      }
    }

    // Check if audio/video is playing in any tab
    const tabs = await Browser.tabs.query({ audible: true })
    if (tabs.length > 0) {
      console.log('🔍 OPTIMIZED: Audio/video playing, postponing scraping')
      return false
    }

    // Check time of day (avoid peak hours)
    const hour = new Date().getHours()
    if (hour >= 9 && hour <= 17) { // Business hours
      console.log('🔍 OPTIMIZED: Peak hours, consider postponing')
      // Don't block entirely, just log
    }

    return true
  }
}

/**
 * Progressive micro-batching strategy
 * Scrapes very small batches frequently instead of large batches rarely
 */
export class MicroBatchScraper {
  private queue: number[] = []
  private isProcessing = false
  private batchSize = 10 // Very small batches
  private delayBetweenBatches = 30000 // 30 seconds

  async addToQueue(startPosition: number, totalCount: number): Promise<void> {
    // Queue positions for scraping
    for (let i = startPosition; i < startPosition + totalCount; i += this.batchSize) {
      this.queue.push(i)
    }

    if (!this.isProcessing) {
      this.processQueue()
    }
  }

  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.queue.length === 0) {
      return
    }

    this.isProcessing = true
    const scraper = new OptimizedScraper()

    while (this.queue.length > 0) {
      const position = this.queue.shift()!
      
      // Check if good time to scrape
      const goodTime = await scraper.isGoodTimeToScrape()
      if (!goodTime) {
        // Re-queue for later
        this.queue.unshift(position)
        await this.delay(60000) // Wait 1 minute
        continue
      }

      // Scrape micro-batch
      console.log(`🔍 MICRO-BATCH: Processing position ${position}`)
      
      // Implementation would call scraper here
      // await scraper.adaptiveScrape(...)

      // Wait before next batch
      if (this.queue.length > 0) {
        await this.delay(this.delayBetweenBatches)
      }
    }

    this.isProcessing = false
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

/**
 * User notification system for scraping operations
 */
export class ScrapingNotifier {
  async notifyBeforeScraping(): Promise<boolean> {
    // Create notification (simplified without buttons due to API limitations)
    const notificationId = await Browser.notifications.create({
      type: 'basic',
      iconUrl: Browser.runtime.getURL('icon-128.png'),
      title: 'Instagram Follower Scraping',
      message: 'Tab will be activated briefly for follower scraping. Click to postpone.',
      requireInteraction: false,
      priority: 1
    } as any) // Type assertion for browser compatibility

    // Wait for user response (with timeout)
    return new Promise((resolve) => {
      let resolved = false
      
      const handleClick = (id: string) => {
        if (id === notificationId && !resolved) {
          resolved = true
          Browser.notifications.clear(notificationId)
          // User clicked notification - postpone
          resolve(false)
        }
      }

      Browser.notifications.onClicked.addListener(handleClick)

      // Auto-continue after 5 seconds if no response
      setTimeout(() => {
        if (!resolved) {
          resolved = true
          Browser.notifications.clear(notificationId)
          resolve(true)
        }
      }, 5000)
    })
  }
}

// Export for use in main background script
export default {
  OptimizedScraper,
  MicroBatchScraper,
  ScrapingNotifier
}