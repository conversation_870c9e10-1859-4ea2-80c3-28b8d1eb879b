# Migration Troubleshooting Guide

## 🚨 Current Issue: Index Already Exists Error

**Error:** `ERROR: relation "AdminSettings_aiProvider_idx" already exists`
**Migration:** `20250815104018_remove_dayofweek_enum`

### 🔧 Resolution Steps

#### Option 1: Mark Migration as Applied (Recommended)

```bash
# Navigate to database package
cd packages/database

# Mark the problematic migration as applied without running it
pnpm prisma migrate resolve --applied 20250815104018_remove_dayofweek_enum

# Continue with remaining migrations
pnpm prisma migrate deploy
```

#### Option 2: Manual Index Fix (If Option 1 Fails)

```bash
# Connect to your PostgreSQL database and run:
psql -d your_database_name

# Check existing indexes
\d "AdminSettings"

# Drop the conflicting index if it exists with the new name
DROP INDEX IF EXISTS "AdminSettings_aiProvider_idx";

# Exit psql
\q

# Now apply the migration
cd packages/database && pnpm prisma migrate deploy
```

#### Option 3: Reset and Rebuild (Development Only)

```bash
# ⚠️ WARNING: This deletes all data
cd packages/database
pnpm prisma migrate reset --force
pnpm prisma migrate deploy
```

### 🔍 Understanding the Issue

The migration `20250815104018_remove_dayofweek_enum` contains:

```sql
-- RenameIndex
ALTER INDEX "IX_AdminSettings_aiProvider" RENAME TO "AdminSettings_aiProvider_idx";
```

But the target index name already exists, likely because:

1. A previous migration already created it
2. Migrations were applied out of order
3. The database has been manually modified

### 📋 General Migration Recovery Process

1. **Check Migration Status**

   ```bash
   cd packages/database && pnpm prisma migrate status
   ```

2. **Identify Failed Migration**
   Look for migrations marked as "Failed" or "Pending"

3. **Resolve Based on Type**

   - **Schema conflicts**: Use `migrate resolve --applied`
   - **Data conflicts**: Fix data manually, then resolve
   - **Missing tables**: Run `migrate deploy` again

4. **Verify Resolution**
   ```bash
   cd packages/database && pnpm prisma migrate status
   ```

### 🛠️ Prevention Tips

- Always backup database before migrations in production
- Test migrations on staging environment first
- Never modify database schema manually in production
- Keep migration files in version control
- Run migrations in sequence, never skip

### 📞 Need More Help?

If the above steps don't work:

1. Check your database logs for more details
2. Verify your DATABASE_URL is correct
3. Ensure you have proper permissions
4. Consider consulting a database administrator for production systems
