# Database Deployment Guide - Instagram Multi-Org System

## 📋 Overview

This guide provides instructions for deploying the Instagram multi-organization database from scratch. The database schema supports multiple organizations with their own Instagram accounts, proper message routing, and AI response capabilities.

## 🔍 Migration Analysis

### Migration History

The database uses **32 migrations** total, starting from a consolidated production schema:

1. **Base Schema** (`20250525185746_consolidated_production_schema`)

   - Core tables: Users, Organizations, Instagram contacts/messages
   - Authentication and authorization system
   - Basic Instagram bot functionality

2. **Feature Additions** (25+ subsequent migrations)

   - Instagram followers management
   - Message batching and follow-up templates
   - Chrome extension workflow
   - Conversation tracking and gathering
   - Processing queues and error handling

3. **Recent Critical Updates**
   - `20250815000000_add_instagram_oauth_fields` - **Instagram OAuth support**
   - `20250812000000_add_ai_provider_settings` - **AI provider configuration**
   - `20250815104018_remove_dayofweek_enum` - **Schema cleanup**

## ✅ Schema Validation

### Critical Fields for Multi-Org Instagram System

**InstagramSettings Table** (Organization-specific Instagram configuration):

```sql
- id: UUID (Primary key)
- organizationId: UUID (Unique, links to Organization)
- instagramAccountId: TEXT (🔑 CRITICAL - Instagram Business Account ID for routing)
- instagramToken: TEXT (Instagram access token)
- instagramUsername: TEXT (Instagram username)
- instagramName: TEXT (Instagram display name)
- instagramProfilePicture: TEXT (Profile picture URL)
- isConnected: BOOLEAN (Connection status)
- tokenExpiresAt: TIMESTAMP (Token expiration)
- isBotEnabled: BOOLEAN (Bot activation per organization)
```

**InstagramMessage Table** (Message storage with direction):

```sql
- isFromUser: BOOLEAN (true = incoming from user, false = outgoing from business)
- isFromExtension: BOOLEAN (messages sent via Chrome extension)
```

**Multi-Organization Support**:

- ✅ Each organization has unique Instagram settings
- ✅ Message routing via `instagramAccountId` field
- ✅ AI responses isolated per organization
- ✅ Complete separation of data between organizations

## 🚀 Deployment Commands

### Prerequisites

```bash
# Ensure you have the required tools
node --version  # Node.js 18+
npm --version   # npm 8+
```

### Environment Setup

Create `.env` file in the project root:

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/instagram_multi_org"

# Instagram API (per organization - configure via UI after deployment)
# These will be set per organization through the admin interface

# AI Configuration (global admin settings)
ANTHROPIC_API_KEY="your_claude_api_key"

# Optional: OpenRouter for alternative AI providers
OPENROUTER_API_KEY="your_openrouter_key"
```

### Single Command Deployment

```bash
# 1. Install dependencies
npm install

# 2. Generate Prisma client and apply all migrations
npm run db:setup
```

**OR manually:**

```bash
# Generate Prisma client
npx prisma generate

# Deploy all migrations to database (creates tables if not exist)
npx prisma migrate deploy

# Optional: Seed database with initial data
npx prisma db seed
```

### Alternative: Reset and Deploy (Development)

```bash
# ⚠️ WARNING: This will delete all existing data
npx prisma migrate reset --force
npx prisma migrate deploy
```

## 🔧 Post-Deployment Configuration

### 1. Admin Settings

Set up global AI configuration:

```sql
INSERT INTO "AdminSettings" (
  id,
  aiProvider,
  useReasoning,
  reasoningBudget,
  maxConversationMessages
) VALUES (
  gen_random_uuid(),
  'claude',
  false,
  4000,
  200
);
```

### 2. Organization Setup

Each organization needs Instagram configuration:

```sql
INSERT INTO "InstagramSettings" (
  id,
  organizationId,
  instagramAccountId,  -- Instagram Business Account ID
  instagramToken,      -- Instagram access token
  isBotEnabled,
  minResponseTime,
  maxResponseTime
) VALUES (
  gen_random_uuid(),
  'your-org-id',
  'instagram-business-account-id',
  'instagram-access-token',
  true,
  30,
  50
);
```

## 📊 Migration Verification

### Check Migration Status

```bash
# View migration history
npx prisma migrate status

# Verify schema matches expected state
npx prisma db pull
npx prisma format
```

### Critical Tables Verification

```sql
-- Verify Instagram settings table structure
\d "InstagramSettings"

-- Check organization isolation
SELECT COUNT(*) as org_count FROM "Organization";
SELECT COUNT(*) as settings_count FROM "InstagramSettings";

-- Verify Instagram account routing capability
SELECT
  o.name as org_name,
  is.instagramAccountId,
  is.instagramUsername,
  is.isBotEnabled
FROM "Organization" o
LEFT JOIN "InstagramSettings" is ON o.id = is.organizationId;
```

## 🛠️ Troubleshooting

### Common Issues

**1. Migration Lock**

```bash
# If migrations are stuck
rm -f packages/database/prisma/migrations/migration_lock.toml
npx prisma migrate resolve --applied [migration-name]
```

**2. Schema Drift**

```bash
# Reset migrations and start fresh (development only)
npx prisma migrate reset
npx prisma db push
```

**3. Connection Issues**

- Verify DATABASE_URL is correct
- Ensure PostgreSQL server is running
- Check firewall/security group settings

**4. Missing Tables**

```bash
# Force apply all migrations
npx prisma migrate deploy --force
```

## 📋 Testing Deployment

### Verify Instagram Multi-Org Functionality

```bash
# Run webhook tests (after deployment)
npm run test:webhooks

# Check multi-organization routing
npm run test:multi-org
```

### Manual Verification

1. Create test organizations
2. Configure Instagram settings for each
3. Send test webhook payloads
4. Verify messages route to correct organizations
5. Test AI responses are organization-specific

## 🔒 Production Considerations

### Security

- Use strong DATABASE_URL credentials
- Rotate Instagram tokens regularly
- Enable row-level security if needed
- Backup database regularly

### Performance

- Database supports indexes for efficient queries
- Consider connection pooling for high traffic
- Monitor AI API usage per organization

### Monitoring

- Set up database monitoring
- Log Instagram API rate limits
- Track webhook processing times
- Monitor AI response success rates

## ✅ Deployment Checklist

- [ ] PostgreSQL database created
- [ ] Environment variables configured
- [ ] Dependencies installed (`npm install`)
- [ ] Prisma client generated (`npx prisma generate`)
- [ ] All migrations applied (`npx prisma migrate deploy`)
- [ ] Admin settings configured
- [ ] Test organizations created
- [ ] Instagram OAuth configured per organization
- [ ] Webhook endpoints tested
- [ ] AI responses verified per organization
- [ ] Multi-organization routing confirmed

## 🎯 Success Criteria

Your deployment is successful when:

1. ✅ All 32 migrations apply without errors
2. ✅ Database schema matches expected structure
3. ✅ Multiple organizations can be created
4. ✅ Each organization has unique Instagram settings
5. ✅ Webhook messages route to correct organizations
6. ✅ AI responses are organization-specific
7. ✅ Echo messages (manual replies) are captured properly

---

**Ready for Production**: Your Instagram multi-organization system is now deployed and ready to handle multiple organizations with their own Instagram accounts, proper message routing, and AI-powered responses.
